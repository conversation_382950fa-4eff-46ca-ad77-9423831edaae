'use client';

import React, { useState, useRef } from 'react';
import { Modal } from '@/components/common/modal';
import { UserProps } from './types';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import { myApi } from '@/api/fetcher';
import { PDFDocument, rgb } from 'pdf-lib';
import { FileText, Upload, Download } from 'lucide-react';
import { generatePractisingPrivilegesTemplate } from '@/lib/pdf-template-generator';

interface PractisingPrivilegesModalProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  user: UserProps;
}

interface FormData {
  fullName: string;
  medicalLicenseNumber: string;
  specialty: string;
  yearsOfExperience: string;
  previousHospital: string;
  contactNumber: string;
  emailAddress: string;
  emergencyContact: string;
  emergencyContactNumber: string;
  requestedPrivileges: string;
  additionalComments: string;
}

export const PractisingPrivilegesModal: React.FC<PractisingPrivilegesModalProps> = ({
  open,
  setOpen,
  user,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [pdfBlob, setPdfBlob] = useState<Blob | null>(null);
  const [formData, setFormData] = useState<FormData>({
    fullName: user.fullName || '',
    medicalLicenseNumber: '',
    specialty: user.doctorProfile?.specialty?.name || '',
    yearsOfExperience: '',
    previousHospital: '',
    contactNumber: user.phoneNumber || '',
    emailAddress: user.email || '',
    emergencyContact: '',
    emergencyContactNumber: '',
    requestedPrivileges: '',
    additionalComments: '',
  });
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const loadPdfTemplate = async (): Promise<Uint8Array> => {
    try {
      // Try to load the PDF from the public folder
      const response = await fetch('/Practising Privileges.pdf');
      if (!response.ok) {
        throw new Error('PDF template not found');
      }
      return new Uint8Array(await response.arrayBuffer());
    } catch (error) {
      // If PDF doesn't exist, create a basic template using our generator
      return generatePractisingPrivilegesTemplate();
    }
  };

  const fillPdfForm = async () => {
    try {
      setIsLoading(true);

      const existingPdfBytes = await loadPdfTemplate();
      const pdfDoc = await PDFDocument.load(existingPdfBytes);

      // Get the first page
      const pages = pdfDoc.getPages();
      const firstPage = pages[0];

      // Try to get form fields if they exist
      const form = pdfDoc.getForm();
      const fields = form.getFields();

      if (fields.length > 0) {
        // Fill form fields if they exist
        fields.forEach(field => {
          const fieldName = field.getName().toLowerCase();

          if (fieldName.includes('name') || fieldName.includes('fullname')) {
            if (field.constructor.name === 'PDFTextField') {
              (field as any).setText(formData.fullName);
            }
          } else if (fieldName.includes('license')) {
            if (field.constructor.name === 'PDFTextField') {
              (field as any).setText(formData.medicalLicenseNumber);
            }
          } else if (fieldName.includes('specialty')) {
            if (field.constructor.name === 'PDFTextField') {
              (field as any).setText(formData.specialty);
            }
          } else if (fieldName.includes('experience')) {
            if (field.constructor.name === 'PDFTextField') {
              (field as any).setText(formData.yearsOfExperience);
            }
          } else if (fieldName.includes('hospital')) {
            if (field.constructor.name === 'PDFTextField') {
              (field as any).setText(formData.previousHospital);
            }
          } else if (fieldName.includes('contact') && !fieldName.includes('emergency')) {
            if (field.constructor.name === 'PDFTextField') {
              (field as any).setText(formData.contactNumber);
            }
          } else if (fieldName.includes('email')) {
            if (field.constructor.name === 'PDFTextField') {
              (field as any).setText(formData.emailAddress);
            }
          } else if (fieldName.includes('emergency') && fieldName.includes('contact') && !fieldName.includes('number')) {
            if (field.constructor.name === 'PDFTextField') {
              (field as any).setText(formData.emergencyContact);
            }
          } else if (fieldName.includes('emergency') && fieldName.includes('number')) {
            if (field.constructor.name === 'PDFTextField') {
              (field as any).setText(formData.emergencyContactNumber);
            }
          } else if (fieldName.includes('privileges')) {
            if (field.constructor.name === 'PDFTextField') {
              (field as any).setText(formData.requestedPrivileges);
            }
          } else if (fieldName.includes('comments')) {
            if (field.constructor.name === 'PDFTextField') {
              (field as any).setText(formData.additionalComments);
            }
          }
        });
      } else {
        // If no form fields, overlay text on the PDF at specific positions
        const fontSize = 9;

        // Position data based on our template layout
        const fieldPositions = [
          { value: formData.fullName, x: 70, y: 680 }, // Full Name
          { value: formData.medicalLicenseNumber, x: 95, y: 660 }, // Medical License
          { value: formData.specialty, x: 70, y: 640 }, // Specialty
          { value: formData.yearsOfExperience, x: 90, y: 620 }, // Years of Experience
          { value: formData.previousHospital, x: 105, y: 600 }, // Previous Hospital
          { value: formData.contactNumber, x: 80, y: 565 }, // Contact Number
          { value: formData.emailAddress, x: 75, y: 545 }, // Email Address
          { value: formData.emergencyContact, x: 95, y: 525 }, // Emergency Contact
          { value: formData.emergencyContactNumber, x: 100, y: 505 }, // Emergency Contact Number
        ];

        // Fill basic fields
        fieldPositions.forEach(field => {
          if (field.value) {
            firstPage.drawText(field.value, {
              x: field.x,
              y: field.y,
              size: fontSize,
              color: rgb(0, 0, 0.8), // Dark blue color
            });
          }
        });

        // Handle multi-line fields
        if (formData.requestedPrivileges) {
          const privilegesLines = formData.requestedPrivileges.split('\n');
          let yPos = 470;
          privilegesLines.slice(0, 3).forEach(line => {
            firstPage.drawText(line, {
              x: 25,
              y: yPos,
              size: fontSize,
              color: rgb(0, 0, 0.8),
            });
            yPos -= 15;
          });
        }

        if (formData.additionalComments) {
          const commentsLines = formData.additionalComments.split('\n');
          let yPos = 410;
          commentsLines.slice(0, 3).forEach(line => {
            firstPage.drawText(line, {
              x: 25,
              y: yPos,
              size: fontSize,
              color: rgb(0, 0, 0.8),
            });
            yPos -= 15;
          });
        }

        // Add current date
        const currentDate = new Date().toLocaleDateString();
        firstPage.drawText(currentDate, {
          x: 145,
          y: 320,
          size: fontSize,
          color: rgb(0, 0, 0.8),
        });
      }

      const pdfBytes = await pdfDoc.save();
      const blob = new Blob([pdfBytes], { type: 'application/pdf' });
      setPdfBlob(blob);

      toast.success('PDF form filled successfully');
    } catch (error) {
      console.error('Error filling PDF:', error);
      toast.error('Failed to fill PDF form');
    } finally {
      setIsLoading(false);
    }
  };

  const downloadFilledPdf = () => {
    if (!pdfBlob) return;
    
    const url = URL.createObjectURL(pdfBlob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `practising-privileges-${user.fullName?.replace(/\s+/g, '-')}.pdf`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const submitApplication = async () => {
    if (!pdfBlob) {
      toast.error('Please fill the PDF form first');
      return;
    }

    try {
      setIsLoading(true);
      
      const formDataToSubmit = new FormData();
      formDataToSubmit.append('practisingPrivilegesPdf', pdfBlob, `practising-privileges-${user.id}.pdf`);
      formDataToSubmit.append('userId', user.id || '');
      formDataToSubmit.append('fullName', formData.fullName);
      formDataToSubmit.append('medicalLicenseNumber', formData.medicalLicenseNumber);
      formDataToSubmit.append('specialty', formData.specialty);
      formDataToSubmit.append('yearsOfExperience', formData.yearsOfExperience);
      formDataToSubmit.append('previousHospital', formData.previousHospital);
      formDataToSubmit.append('contactNumber', formData.contactNumber);
      formDataToSubmit.append('emailAddress', formData.emailAddress);
      formDataToSubmit.append('emergencyContact', formData.emergencyContact);
      formDataToSubmit.append('emergencyContactNumber', formData.emergencyContactNumber);
      formDataToSubmit.append('requestedPrivileges', formData.requestedPrivileges);
      formDataToSubmit.append('additionalComments', formData.additionalComments);

      const response = await myApi.patch('/staff/update', formDataToSubmit, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      if (response.status === 200) {
        toast.success('Practising privileges application submitted successfully');
        setOpen(false);
        // Reset form
        setPdfBlob(null);
      }
    } catch (error: any) {
      console.error('Error submitting application:', error);
      toast.error(error?.response?.data?.message || 'Failed to submit application');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Modal
      open={open}
      setOpen={setOpen}
      title="Practising Privileges Application"
      description="Fill out the practising privileges form and submit your application"
      size="lg"
    >
      <div className="space-y-4 max-h-[70vh] overflow-y-auto">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="fullName">Full Name</Label>
            <Input
              id="fullName"
              value={formData.fullName}
              onChange={(e) => handleInputChange('fullName', e.target.value)}
              placeholder="Enter full name"
            />
          </div>
          
          <div>
            <Label htmlFor="medicalLicenseNumber">Medical License Number</Label>
            <Input
              id="medicalLicenseNumber"
              value={formData.medicalLicenseNumber}
              onChange={(e) => handleInputChange('medicalLicenseNumber', e.target.value)}
              placeholder="Enter medical license number"
            />
          </div>
          
          <div>
            <Label htmlFor="specialty">Specialty</Label>
            <Input
              id="specialty"
              value={formData.specialty}
              onChange={(e) => handleInputChange('specialty', e.target.value)}
              placeholder="Enter specialty"
            />
          </div>
          
          <div>
            <Label htmlFor="yearsOfExperience">Years of Experience</Label>
            <Input
              id="yearsOfExperience"
              value={formData.yearsOfExperience}
              onChange={(e) => handleInputChange('yearsOfExperience', e.target.value)}
              placeholder="Enter years of experience"
            />
          </div>
          
          <div>
            <Label htmlFor="previousHospital">Previous Hospital</Label>
            <Input
              id="previousHospital"
              value={formData.previousHospital}
              onChange={(e) => handleInputChange('previousHospital', e.target.value)}
              placeholder="Enter previous hospital"
            />
          </div>
          
          <div>
            <Label htmlFor="contactNumber">Contact Number</Label>
            <Input
              id="contactNumber"
              value={formData.contactNumber}
              onChange={(e) => handleInputChange('contactNumber', e.target.value)}
              placeholder="Enter contact number"
            />
          </div>
          
          <div>
            <Label htmlFor="emailAddress">Email Address</Label>
            <Input
              id="emailAddress"
              value={formData.emailAddress}
              onChange={(e) => handleInputChange('emailAddress', e.target.value)}
              placeholder="Enter email address"
            />
          </div>
          
          <div>
            <Label htmlFor="emergencyContact">Emergency Contact</Label>
            <Input
              id="emergencyContact"
              value={formData.emergencyContact}
              onChange={(e) => handleInputChange('emergencyContact', e.target.value)}
              placeholder="Enter emergency contact name"
            />
          </div>
          
          <div>
            <Label htmlFor="emergencyContactNumber">Emergency Contact Number</Label>
            <Input
              id="emergencyContactNumber"
              value={formData.emergencyContactNumber}
              onChange={(e) => handleInputChange('emergencyContactNumber', e.target.value)}
              placeholder="Enter emergency contact number"
            />
          </div>
        </div>
        
        <div>
          <Label htmlFor="requestedPrivileges">Requested Privileges</Label>
          <Input
            id="requestedPrivileges"
            value={formData.requestedPrivileges}
            onChange={(e) => handleInputChange('requestedPrivileges', e.target.value)}
            placeholder="Enter requested privileges"
          />
        </div>
        
        <div>
          <Label htmlFor="additionalComments">Additional Comments</Label>
          <Input
            id="additionalComments"
            value={formData.additionalComments}
            onChange={(e) => handleInputChange('additionalComments', e.target.value)}
            placeholder="Enter additional comments"
          />
        </div>
        
        <div className="flex flex-col sm:flex-row gap-2 pt-4">
          <Button
            onClick={fillPdfForm}
            disabled={isLoading}
            className="flex-1"
          >
            <FileText className="w-4 h-4 mr-2" />
            {isLoading ? 'Filling PDF...' : 'Fill PDF Form'}
          </Button>
          
          {pdfBlob && (
            <Button
              onClick={downloadFilledPdf}
              variant="outline"
              className="flex-1"
            >
              <Download className="w-4 h-4 mr-2" />
              Download PDF
            </Button>
          )}
          
          {pdfBlob && (
            <Button
              onClick={submitApplication}
              disabled={isLoading}
              className="flex-1"
            >
              <Upload className="w-4 h-4 mr-2" />
              {isLoading ? 'Submitting...' : 'Submit Application'}
            </Button>
          )}
        </div>
      </div>
    </Modal>
  );
};
