'use client';

import React, { useEffect, useState } from 'react';

interface WelcomeScreenProps {
  onComplete: () => void;
}

export function WelcomeScreen({ onComplete }: WelcomeScreenProps) {
  const [showTagline, setShowTagline] = useState(false);
  const [showContent, setShowContent] = useState(false);

  useEffect(() => {
    const contentTimer = setTimeout(() => setShowContent(true), 100);
    const taglineTimer = setTimeout(() => setShowTagline(true), 1500);
    const completeTimer = setTimeout(() => onComplete(), 8000);

    return () => {
      clearTimeout(contentTimer);
      clearTimeout(taglineTimer);
      clearTimeout(completeTimer);
    };
  }, [onComplete]);

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <div className="text-center space-y-4">
        {/* Hi! Text */}
        <div
          className={`relative ${showContent ? 'animate-scale-in' : 'opacity-0'}`}
        >
          <h1 className="text-6xl font-bold bg-gradient-to-r from-[#222E65] via-[#BD9A3D] to-[#222E65] bg-clip-text text-transparent animate-gradient">
            CHL-IS
          </h1>

          {/* Sparkles */}
          <div className="absolute top-0 right-2 text-[#BD9A3D] text-4xl animate-sparkle">
            ✨
          </div>
          <div className="absolute bottom-0 left-2 text-[#BD9A3D] text-3xl animate-sparkle-reverse">
            💫
          </div>
        </div>

        {/* Tagline */}
        <div
          className={`space-y-1 ${showTagline ? 'animate-slide-up' : 'opacity-0'}`}
        >
          <p className="px-4 text-xs sm:text-base font-medium uppercase text-[#222E65] dark:text-[#BD9A3D] animate-pulse-text">
            Cedarcrest Hospitals Limited Innovation Suite
          </p>
          <p className="px-4 text-xs md:text-sm text-[#222E65] dark:text-[#BD9A3D] animate-pulse-text">
            A product of innovations and digital strategy
          </p>
        </div>

        {/* Loading dots */}
        <div
          className={`flex justify-center space-x-2 transition-opacity duration-500 delay-1000 ${showTagline ? 'opacity-100' : 'opacity-0'}`}
        >
          {[0, 0.2, 0.4].map((delay, idx) => (
            <div
              key={idx}
              className="w-3 h-3 rounded-full bg-[#BD9A3D] animate-dot-pulse"
              style={{ animationDelay: `${delay}s` }}
            />
          ))}
        </div>
      </div>

      {/* Animations */}
      <style jsx>{`
        @keyframes scaleIn {
          0% {
            transform: scale(0);
            opacity: 0;
          }
          100% {
            transform: scale(1);
            opacity: 1;
          }
        }

        @keyframes gradientShift {
          0%,
          100% {
            background-position: 0% 50%;
          }
          50% {
            background-position: 100% 50%;
          }
        }

        @keyframes sparkleRotate {
          0% {
            transform: rotate(0deg) scale(1);
          }
          50% {
            transform: rotate(180deg) scale(1.2);
          }
          100% {
            transform: rotate(360deg) scale(1);
          }
        }

        @keyframes sparkleRotateReverse {
          0% {
            transform: rotate(360deg) scale(1);
          }
          50% {
            transform: rotate(180deg) scale(1.3);
          }
          100% {
            transform: rotate(0deg) scale(1);
          }
        }

        @keyframes slideUp {
          0% {
            transform: translateY(30px);
            opacity: 0;
          }
          100% {
            transform: translateY(0);
            opacity: 1;
          }
        }

        @keyframes pulseText {
          0%,
          100% {
            opacity: 0.7;
          }
          50% {
            opacity: 1;
          }
        }

        @keyframes dotPulse {
          0%,
          100% {
            transform: scale(1);
            opacity: 0.5;
          }
          50% {
            transform: scale(1.5);
            opacity: 1;
          }
        }

        .animate-scale-in {
          animation: scaleIn 1.2s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        }

        .animate-gradient {
          background-size: 200% 200%;
          animation: gradientShift 3s linear infinite;
        }

        .animate-sparkle {
          animation: sparkleRotate 2s ease-in-out infinite;
        }

        .animate-sparkle-reverse {
          animation: sparkleRotateReverse 2.5s ease-in-out infinite;
          animation-delay: 0.5s;
        }

        .animate-slide-up {
          animation: slideUp 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        }

        .animate-pulse-text {
          animation: pulseText 2s ease-in-out infinite;
        }

        .animate-dot-pulse {
          animation: dotPulse 1.5s ease-in-out infinite;
        }
      `}</style>
    </div>
  );
}
