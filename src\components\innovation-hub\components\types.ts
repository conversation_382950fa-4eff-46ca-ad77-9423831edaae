import * as z from 'zod';

// Schema for the idea submission form
export const ideaSchema = z.object({
  title: z.string().min(5, {
    message: 'Title must be at least 5 characters long.',
  }),
  description: z.string().min(20, {
    message: 'Description must be at least 20 characters long.',
  }),
});

export type IdeaFormValues = z.infer<typeof ideaSchema>;

// Schema for the comment submission form
export const commentSchema = z.object({
  comment: z
    .string()
    .min(1, { message: 'Comment cannot be empty.' })
    .max(280, { message: 'Comment is too long.' }),
});
export type CommentFormValues = z.infer<typeof commentSchema>;

// Dummy data for ideas
export const initialIdeas = [
  {
    id: '1',
    title: 'Gamified Onboarding Process',
    description:
      'Create a gamified onboarding experience for new hires to make learning about the company more engaging. New employees could earn points and badges for completing modules.',
    author: {
      name: '<PERSON>',
      avatar: 'https://github.com/shadcn.png',
    },
    createdAt: '2 days ago',
    likes: 15,
    comments: [
      {
        id: 'c1-1',
        author: { name: '<PERSON>', avatar: 'https://github.com/vercel.png' },
        text: 'This is a fantastic idea! It would make the onboarding process so much more engaging and memorable for new team members.',
        createdAt: '2 days ago',
      },
      {
        id: 'c1-2',
        author: {
          name: 'Alice Johnson',
          avatar: 'https://github.com/nextjs.png',
        },
        text: "I agree. We could even tie it into our company values and culture from day one. I'm happy to help brainstorm some of the game mechanics.",
        createdAt: '1 day ago',
      },
    ],
    status: 'Approved' as const,
  },
  {
    id: '2',
    title: 'AI-Powered Internal Helpdesk',
    description:
      'Implement an AI chatbot that can instantly answer common employee questions about HR policies, IT issues, and company procedures. This would free up support staff for more complex issues.',
    author: {
      name: 'John Smith',
      avatar: 'https://github.com/vercel.png',
    },
    createdAt: '5 days ago',
    likes: 28,
    comments: [
      {
        id: 'c2-1',
        author: { name: 'Jane Doe', avatar: 'https://github.com/shadcn.png' },
        text: 'This could be a huge time-saver for the support teams. We should look into existing solutions we can integrate.',
        createdAt: '4 days ago',
      },
    ],
    status: 'Under Review' as const,
  },
  {
    id: '3',
    title: 'Sustainable Office Initiatives',
    description:
      "Launch a program to reduce our office's carbon footprint. Ideas include a better recycling program, reducing paper usage with more digital workflows, and using energy-efficient lighting.",
    author: {
      name: 'Alice Johnson',
      avatar: 'https://github.com/nextjs.png',
    },
    createdAt: '1 week ago',
    likes: 42,
    comments: [],
    status: 'New' as const,
  },
];

export type Idea = (typeof initialIdeas)[0];
export type IdeaComment = Idea['comments'][0];
export type IdeaStatus = Idea['status'];

export const ideaStatuses: IdeaStatus[] = ['New', 'Under Review', 'Approved'];

