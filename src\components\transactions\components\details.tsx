import { Modal } from '@/components/common/modal';
import React, { useState, useCallback } from 'react';
import { ModalProps } from '@/components/types';
import { numberFormat, cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { TicketCheck, User, Settings } from 'lucide-react';
import dayjs from 'dayjs';
import { StatusBadge } from '@/components/common/status-badge';
import { hasPermission, PERMISSIONS } from '@/lib/types/permissions';
import { myApi } from '@/api/fetcher';
import { toast } from 'sonner';

// Tab interface
interface TabProps {
  label: string;
  icon: React.ReactNode;
  content: React.ReactNode;
}

const Details: React.FC<ModalProps> = ({ setOpen, open, mutate, data }) => {
  const [activeTab, setActiveTab] = useState(0);
  const [selectedStatus, setSelectedStatus] = useState('');
  const [remarks, setRemarks] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);

  const canEditTransactions = hasPermission(PERMISSIONS.TRANSACTION_EDIT);

  if (!data) return null;

  console.log(data, 'Transactionnaa');

  // Transaction Details Tab Content
  const TransactionInfo = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-xs font-medium mb-2 text-gray-500 uppercase">
          Basic Information
        </h3>
        <div className="grid sm:grid-cols-2 gap-4 text-sm">
          <div>
            <p className="text-gray-500 dark:text-gray-400">Total Amount</p>
            <p className="font-medium text-lg">{numberFormat(data.amount)}</p>
          </div>
          <div>
            <p className="text-gray-500 dark:text-gray-400">Reference</p>
            <p className="font-medium">{data.reference || 'N/A'}</p>
          </div>
          <div>
            <p className="text-gray-500 dark:text-gray-400">Date</p>
            <p className="font-medium">
              {dayjs(data.createdAt).format('MMMM D, YYYY h:mm A')}
            </p>
          </div>
          <div>
            <p className="text-gray-500 dark:text-gray-400">Status</p>
            <div className="mt-1">
              {StatusBadge({
                status: data.status.toLowerCase(),
              })}
            </div>
          </div>
        </div>
      </div>

      <div>
        <h3 className="text-xs font-medium mb-2 text-gray-500 uppercase">
          Transaction Details
        </h3>
        <div className="grid sm:grid-cols-2 gap-4 text-sm">
          <div>
            <p className="text-gray-500 dark:text-gray-400">Type</p>
            <div className="flex text-xs items-center gap-2 mt-1">
              <span className="px-2 py-1 rounded-full text-[11px] bg-gray-100 text-gray-600 font-semibold">
                {data.type}
              </span>
            </div>
          </div>
          <div>
            <p className="text-gray-500 dark:text-gray-400">Mode</p>
            <p className="font-medium capitalize">{data.mode || 'N/A'}</p>
          </div>
          <div>
            <p className="text-gray-500 dark:text-gray-400">Service Amount</p>
            <p className="font-medium text-lg">
              {numberFormat(data.amount - data.charges)}
            </p>
          </div>
          <div>
            <p className="text-gray-500 dark:text-gray-400">Charges</p>
            <p className="font-medium text-lg">{numberFormat(data.charges)}</p>
          </div>
          {data.remarks && (
            <div className="sm:col-span-2">
              <p className="text-gray-500 dark:text-gray-400">Description</p>
              <p className="font-medium">{data.remarks}</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );

  // User Information Tab Content (if available)
  const UserInfo = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-xs font-medium mb-2 text-gray-500 uppercase">
          From Account
        </h3>
        <div className="text-sm">
          {data.fromAccount?.user && (
            <div className="grid sm:grid-cols-2 gap-4">
              <div>
                <p className="text-gray-500 dark:text-gray-400">
                  Full Name/UHID
                </p>
                <p className="font-medium">
                  {(data.fromAccount.user?.title
                    ? data.fromAccount.user.title + ' '
                    : '') +
                    (data.fromAccount.user?.firstName
                      ? data.fromAccount.user.firstName + ' '
                      : '') +
                    (data.fromAccount.user?.lastName || '') ||
                    data.fromAccount.user.uhid}
                </p>
              </div>
              <div>
                <p className="text-gray-500 dark:text-gray-400">Email</p>
                <p className="font-medium">
                  {data.fromAccount.user?.emailAddress || 'N/A'}
                </p>
              </div>
              <div>
                <p className="text-gray-500 dark:text-gray-400">Phone</p>
                <p className="font-medium">
                  {data.fromAccount.user?.phoneNumber || 'N/A'}
                </p>
              </div>
            </div>
          )}
          {data.fromAccount?.staff && (
            <div className="grid sm:grid-cols-2 gap-4">
              <div>
                <p className="text-gray-500 dark:text-gray-400">Staff Name</p>
                <p className="font-medium">{data.fromAccount.staff.fullName}</p>
              </div>
              <div>
                <p className="text-gray-500 dark:text-gray-400">Staff ID</p>
                <p className="font-medium">
                  {data.fromAccount.staff.staffCode || 'N/A'}
                </p>
              </div>
            </div>
          )}
          {data.fromAccount?.system && (
            <div>
              <p className="text-gray-500 dark:text-gray-400">System</p>
              <p className="font-medium">
                {data.fromAccount.system.name || 'System Account'}
              </p>
            </div>
          )}
          {!data.fromAccount?.user &&
            !data.fromAccount?.staff &&
            !data.fromAccount?.system && (
              <p className="font-medium">{data.fromAccount || 'N/A'}</p>
            )}
        </div>
      </div>

      <div>
        <h3 className="text-xs font-medium mb-2 text-gray-500 uppercase">
          To Account
        </h3>
        <div className="text-sm">
          {data.toAccount?.user && (
            <div className="grid sm:grid-cols-2 gap-4">
              <div>
                <p className="text-gray-500 dark:text-gray-400">
                  Full Name/UHID
                </p>
                <p className="font-medium">
                  {(data.fromAccount.user?.title
                    ? data.fromAccount.user.title + ' '
                    : '') +
                    (data.fromAccount.user?.firstName
                      ? data.fromAccount.user.firstName + ' '
                      : '') +
                    (data.fromAccount.user?.lastName || '') ||
                    data.fromAccount.user.uhid}
                </p>
              </div>
              <div>
                <p className="text-gray-500 dark:text-gray-400">Email</p>
                <p className="font-medium">
                  {data.fromAccount.user?.emailAddress || 'N/A'}
                </p>
              </div>
              <div>
                <p className="text-gray-500 dark:text-gray-400">Phone</p>
                <p className="font-medium">
                  {data.fromAccount.user?.phoneNumber || 'N/A'}
                </p>
              </div>
            </div>
          )}
          {data.toAccount?.staff && (
            <div className="grid sm:grid-cols-2 gap-4">
              <div>
                <p className="text-gray-500 dark:text-gray-400">Staff Name</p>
                <p className="font-medium">{data.toAccount.staff.fullName}</p>
              </div>
              <div>
                <p className="text-gray-500 dark:text-gray-400">Staff ID</p>
                <p className="font-medium">
                  {data.toAccount.staff.staffCode || 'N/A'}
                </p>
              </div>
            </div>
          )}
          {data.toAccount?.system && (
            <div>
              <p className="text-gray-500 dark:text-gray-400">System</p>
              <p className="font-medium">
                {data.toAccount.system.name || 'System Account'}
              </p>
            </div>
          )}
          {!data.toAccount?.user &&
            !data.toAccount?.staff &&
            !data.toAccount?.system && (
              <p className="font-medium">{data.toAccount || 'N/A'}</p>
            )}
        </div>
      </div>
    </div>
  );

  const handleProcessTransaction = async () => {
    if (!selectedStatus || !remarks.trim()) {
      toast.error('Please select status and enter remarks');
      return;
    }
    const payload = {
      status: selectedStatus,
      remarks: remarks.trim(),
      transactionId: data.id,
    };

    console.log(payload);

    setIsProcessing(true);
    try {
      const res = await myApi.patch(
        `/transaction/withdrawal-request-update`,
        payload
      );

      if (res.status === 200) {
        toast.success('Transaction processed successfully');
        setOpen(false);
        if (mutate) mutate();
      }
    } catch (error) {
      console.log('Failed to process transaction');
    } finally {
      setIsProcessing(false);
    }
  };

  const getStatusOptions = () => {
    if (data.status.toLowerCase() === 'pending') {
      return [{ value: 'PROCESSING', label: 'Processing' }];
    }
    if (data.status.toLowerCase() === 'processing') {
      return [
        { value: 'SUCCESS', label: 'Success' },
        { value: 'FAILED', label: 'Failed' },
        { value: 'CANCELLED', label: 'Cancelled' },
      ];
    }
    return [];
  };

  const statusOptions = getStatusOptions();

  // Define tabs
  const tabs: TabProps[] = [
    {
      label: 'Transaction',
      icon: <TicketCheck className="w-4 h-4" />,
      content: <TransactionInfo />,
    },
    {
      label: 'Account',
      icon: <User className="w-4 h-4" />,
      content: <UserInfo />,
    },
  ];

  // Add process tab if user has permission
  if (canEditTransactions) {
    tabs.push({
      label: 'Process',
      icon: <Settings className="w-4 h-4" />,
      content:
        statusOptions.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            This transaction cannot be processed in its current state.
          </div>
        ) : (
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium mb-2 block">
                Update Status
              </label>
              <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                <SelectTrigger>
                  <SelectValue placeholder="Select new status" />
                </SelectTrigger>
                <SelectContent>
                  {statusOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-sm font-medium mb-2 block">Remarks</label>
              <Textarea
                placeholder="Enter short remarks..."
                value={remarks}
                onChange={(e) => setRemarks(e.target.value)}
                rows={3}
              />
            </div>

            <Button
              onClick={handleProcessTransaction}
              disabled={!selectedStatus || !remarks.trim() || isProcessing}
              className="w-full"
            >
              {isProcessing ? 'Processing...' : 'Process Transaction'}
            </Button>
          </div>
        ),
    });
  }

  return (
    <Modal
      open={open}
      setOpen={setOpen}
      title="Transaction Details"
      description={`Transaction with reference ${data.reference}`}
    >
      <>
        {/* Tabs */}
        <div className="flex border-b mb-4">
          {tabs.map((tab, index) => (
            <button
              key={index}
              className={cn(
                'flex items-center gap-1 px-4 py-2 text-sm font-medium',
                activeTab === index
                  ? 'border-b-2 border-primary text-primary'
                  : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
              )}
              onClick={() => setActiveTab(index)}
            >
              {tab.icon}
              {tab.label}
            </button>
          ))}
        </div>

        {/* Tab content */}
        <div className="py-2">{tabs[activeTab].content}</div>

        {/* Action buttons */}
        <div className="flex justify-end mt-4">
          <Button variant="outline" onClick={() => setOpen(false)}>
            Close
          </Button>
        </div>
      </>
    </Modal>
  );
};

export default Details;
