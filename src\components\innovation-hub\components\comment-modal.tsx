'use client';

import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Modal } from '@/components/common/modal';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from '@/components/ui/form';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Idea, CommentFormValues, commentSchema } from '../components/types';

export const CommentModal = ({
  idea,
  open,
  onOpenChange,
  onCommentSubmit,
}: {
  idea: Idea | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onCommentSubmit: (commentText: string) => void;
}) => {
  const form = useForm<CommentFormValues>({
    resolver: zodResolver(commentSchema),
    defaultValues: { comment: '' },
  });

  if (!idea) return null;

  const onSubmit = (values: CommentFormValues) => {
    onCommentSubmit(values.comment);
    form.reset();
  };

  return (
    <Modal
      open={open}
      setOpen={onOpenChange}
      title={`Comments on "${idea.title}"`}
      description="Read and contribute to the discussion."
      size="lg"
    >
      <div className="space-y-4 max-h-[60vh] overflow-y-auto pr-4">
        {idea.comments.length > 0 ? (
          idea.comments.map((comment) => (
            <div key={comment.id} className="flex items-start space-x-3">
              <Avatar>
                <AvatarImage
                  src={comment.author.avatar}
                  alt={comment.author.name}
                />
                <AvatarFallback>
                  {comment.author.name
                    .split(' ')
                    .map((n) => n[0])
                    .join('')}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1">
                <div className="bg-gray-100 dark:bg-gray-800 rounded-lg p-3">
                  <p className="font-semibold text-sm">{comment.author.name}</p>
                  <p className="text-sm">{comment.text}</p>
                </div>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  {comment.createdAt}
                </p>
              </div>
            </div>
          ))
        ) : (
          <p className="text-center text-gray-500 dark:text-gray-400 py-8">
            No comments yet. Be the first to share your thoughts!
          </p>
        )}
      </div>
      <div className="mt-6 border-t pt-4">
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="flex items-start space-x-3"
          >
            <FormField
              control={form.control}
              name="comment"
              render={({ field }) => (
                <FormItem className="flex-1">
                  <FormControl>
                    <Textarea
                      placeholder="Add your comment..."
                      className="resize-none"
                      rows={2}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <Button type="submit">Comment</Button>
          </form>
        </Form>
      </div>
    </Modal>
  );
};

