{"name": "dashboard2", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --write ."}, "dependencies": {"@hookform/resolvers": "^4.1.3", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-tooltip": "^1.2.6", "axios": "^1.8.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.0", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "input-otp": "^1.4.2", "jspdf": "^2.5.1", "jspdf-autotable": "^3.8.2", "lucide-react": "^0.513.0", "next": "15.2.1", "next-pwa": "^5.6.0", "next-themes": "^0.4.6", "next13-progressbar": "^1.2.2", "papaparse": "^5.4.1", "react": "^19.0.0", "react-day-picker": "^9.8.0", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "recharts": "^2.15.1", "socket.io-client": "^4.8.1", "sonner": "^2.0.2", "swr": "^2.3.2", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "valtio": "^2.1.4", "vaul": "^1.1.2", "xlsx": "^0.18.5", "zod": "^3.24.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/next-pwa": "^5.6.9", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.2.1", "prettier": "^3.5.3", "tailwindcss": "^4", "typescript": "^5"}}