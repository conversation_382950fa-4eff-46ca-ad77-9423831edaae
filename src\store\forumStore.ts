import { proxy } from 'valtio';

export interface UserPresence {
  userId: string;
  userName: string;
  status: 'online' | 'offline' | 'away' | 'busy';
  lastSeen: string;
  avatar?: string;
  emoji?: string;
  customMessage?: string;
  clearAfter?: Date;
}

export interface TypingIndicator {
  userId: string;
  userName: string;
  groupId?: string;
  recipientId?: string;
  isTyping: boolean;
  timestamp: string;
}

export interface MessageReaction {
  messageId: string;
  userId: string;
  userName: string;
  emoji: string;
  timestamp: string;
}

export interface OnlineUser {
  userId: string;
  userName: string;
  avatar?: string;
  status: 'online' | 'away' | 'busy';
  lastSeen: string;
}

interface ForumState {
  // User presence
  userPresence: Map<string, UserPresence>;
  onlineUsers: OnlineUser[];

  // Typing indicators
  typingIndicators: Map<string, TypingIndicator[]>; // key: groupId or recipientId

  // Message reactions
  messageReactions: Map<string, MessageReaction[]>; // key: messageId

  // Connection status
  isConnected: boolean;
  connectionStatus:
    | 'connecting'
    | 'connected'
    | 'disconnected'
    | 'reconnecting'
    | 'offline';

  // Active conversations
  activeGroupId: string | null;
  activeRecipientId: string | null;

  // Unread counts
  unreadCounts: Map<string, number>; // key: groupId or userId for DMs

  // Methods
  setUserPresence: (userId: string, presence: UserPresence) => void;
  removeUserPresence: (userId: string) => void;
  setTypingIndicator: (key: string, indicator: TypingIndicator) => void;
  removeTypingIndicator: (key: string, userId: string) => void;
  clearTypingIndicators: (key: string) => void;
  addMessageReaction: (messageId: string, reaction: MessageReaction) => void;
  removeMessageReaction: (
    messageId: string,
    userId: string,
    emoji: string
  ) => void;
  setConnectionStatus: (
    status:
      | 'connecting'
      | 'connected'
      | 'disconnected'
      | 'reconnecting'
      | 'offline'
  ) => void;
  setActiveGroup: (groupId: string | null) => void;
  setActiveRecipient: (recipientId: string | null) => void;
  incrementUnreadCount: (key: string) => void;
  resetUnreadCount: (key: string) => void;
  getUnreadCount: (key: string) => number;
  getOnlineUsersInGroup: (groupId: string) => OnlineUser[];
  getTypingUsersInGroup: (groupId: string) => TypingIndicator[];
  getTypingUsersInDM: (recipientId: string) => TypingIndicator[];
}

export const forumStore: ForumState = proxy<ForumState>({
  userPresence: new Map(),
  onlineUsers: [],
  typingIndicators: new Map(),
  messageReactions: new Map(),
  isConnected: false,
  connectionStatus: 'disconnected',
  activeGroupId: null,
  activeRecipientId: null,
  unreadCounts: new Map(),

  setUserPresence: (userId: string, presence: UserPresence) => {
    forumStore.userPresence.set(userId, presence);

    // Update online users list
    const existingIndex = forumStore.onlineUsers.findIndex(
      (user) => user.userId === userId
    );
    if (
      presence.status === 'online' ||
      presence.status === 'away' ||
      presence.status === 'busy'
    ) {
      const onlineUser: OnlineUser = {
        userId: presence.userId,
        userName: presence.userName,
        avatar: presence.avatar,
        status: presence.status,
        lastSeen: presence.lastSeen,
      };

      if (existingIndex >= 0) {
        forumStore.onlineUsers[existingIndex] = onlineUser;
      } else {
        forumStore.onlineUsers.push(onlineUser);
      }
    } else if (existingIndex >= 0) {
      forumStore.onlineUsers.splice(existingIndex, 1);
    }
  },

  removeUserPresence: (userId: string) => {
    forumStore.userPresence.delete(userId);
    const index = forumStore.onlineUsers.findIndex(
      (user) => user.userId === userId
    );
    if (index >= 0) {
      forumStore.onlineUsers.splice(index, 1);
    }
  },

  setTypingIndicator: (key: string, indicator: TypingIndicator) => {
    const indicators = forumStore.typingIndicators.get(key) || [];
    const existingIndex = indicators.findIndex(
      (ind) => ind.userId === indicator.userId
    );

    if (indicator.isTyping) {
      if (existingIndex >= 0) {
        indicators[existingIndex] = indicator;
      } else {
        indicators.push(indicator);
      }
    } else if (existingIndex >= 0) {
      indicators.splice(existingIndex, 1);
    }

    forumStore.typingIndicators.set(key, indicators);
  },

  removeTypingIndicator: (key: string, userId: string) => {
    const indicators = forumStore.typingIndicators.get(key) || [];
    const filteredIndicators = indicators.filter(
      (ind) => ind.userId !== userId
    );
    forumStore.typingIndicators.set(key, filteredIndicators);
  },

  clearTypingIndicators: (key: string) => {
    forumStore.typingIndicators.set(key, []);
  },

  addMessageReaction: (messageId: string, reaction: MessageReaction) => {
    const reactions = forumStore.messageReactions.get(messageId) || [];
    const existingIndex = reactions.findIndex(
      (r) => r.userId === reaction.userId && r.emoji === reaction.emoji
    );

    if (existingIndex >= 0) {
      reactions[existingIndex] = reaction;
    } else {
      reactions.push(reaction);
    }

    forumStore.messageReactions.set(messageId, reactions);
  },

  removeMessageReaction: (messageId: string, userId: string, emoji: string) => {
    const reactions = forumStore.messageReactions.get(messageId) || [];
    const filteredReactions = reactions.filter(
      (r) => !(r.userId === userId && r.emoji === emoji)
    );
    forumStore.messageReactions.set(messageId, filteredReactions);
  },

  setConnectionStatus: (
    status:
      | 'connecting'
      | 'connected'
      | 'disconnected'
      | 'reconnecting'
      | 'offline'
  ) => {
    forumStore.connectionStatus = status;
    forumStore.isConnected = status === 'connected';
  },

  setActiveGroup: (groupId: string | null) => {
    forumStore.activeGroupId = groupId;
    forumStore.activeRecipientId = null;
    if (groupId) {
      forumStore.resetUnreadCount(groupId);
    }
  },

  setActiveRecipient: (recipientId: string | null) => {
    forumStore.activeRecipientId = recipientId;
    forumStore.activeGroupId = null;
    if (recipientId) {
      forumStore.resetUnreadCount(recipientId);
    }
  },

  incrementUnreadCount: (key: string) => {
    const current = forumStore.unreadCounts.get(key) || 0;
    forumStore.unreadCounts.set(key, current + 1);
  },

  resetUnreadCount: (key: string) => {
    forumStore.unreadCounts.set(key, 0);
  },

  getUnreadCount: (key: string) => {
    return forumStore.unreadCounts.get(key) || 0;
  },

  getOnlineUsersInGroup: (groupId: string) => {
    // This would typically filter based on group membership
    // For now, return all online users
    return forumStore.onlineUsers;
  },

  getTypingUsersInGroup: (groupId: string) => {
    return forumStore.typingIndicators.get(groupId) || [];
  },

  getTypingUsersInDM: (recipientId: string) => {
    return forumStore.typingIndicators.get(recipientId) || [];
  },
});
