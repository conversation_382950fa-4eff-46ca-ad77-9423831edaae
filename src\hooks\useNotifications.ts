'use client';

import { useSnapshot } from 'valtio';
import { useEffect, useCallback } from 'react';
import { notificationStore } from '@/store/notificationStore';
import {
  GetUserNotifications,
  GetUnreadNotificationCount,
  markNotificationAsRead as apiMarkAsRead,
  markAllNotificationsAsRead as apiMarkAllAsRead,
  deleteNotification as apiDeleteNotification,
  clearAllNotifications as apiClearAll,
} from '@/api/notifications/data';

export function useNotifications() {
  const snapshot = useSnapshot(notificationStore);
  const {
    notifications,
    isLoading,
    lastSyncTime,
    addNotification,
    markAsRead: localMarkAsRead,
    markAllAsRead: localMarkAllAsRead,
    removeNotification: localRemoveNotification,
    clearAll: localClearAll,
  } = snapshot;

  // Fetch notifications from backend
  const {
    notifications: backendNotifications,
    isLoading: backendLoading,
    mutate: refetchNotifications,
  } = GetUserNotifications();

  const { mutate: refetchUnreadCount } = GetUnreadNotificationCount();

  // Define refresh function early so it can be used in useEffect
  const refresh = useCallback(() => {
    refetchNotifications();
    refetchUnreadCount();
  }, [refetchNotifications, refetchUnreadCount]);

  // Sync with backend on mount and when backend data changes
  useEffect(() => {
    if (backendNotifications && backendNotifications.length >= 0) {
      notificationStore.syncWithBackend(backendNotifications);
    }
  }, [backendNotifications]);

  // Update loading state
  useEffect(() => {
    notificationStore.setLoading(backendLoading);
  }, [backendLoading]);

  // Listen for WebSocket sync requests
  useEffect(() => {
    const handleSyncRequest = () => {
      refresh();
    };

    window.addEventListener('notification-sync-requested', handleSyncRequest);

    return () => {
      window.removeEventListener(
        'notification-sync-requested',
        handleSyncRequest
      );
    };
  }, [refresh]);

  const unreadCount = notifications.filter((n) => !n.read).length;

  // Enhanced methods that sync with backend
  const markAsRead = useCallback(
    async (id: string) => {
      // Check if notification is already read to avoid unnecessary API calls
      const notification = notifications.find((n) => n.id === id);
      if (notification?.read) {
        return; // Already read, no need to call API
      }

      // Optimistic update
      localMarkAsRead(id);

      try {
        await apiMarkAsRead(id);
        refetchNotifications();
        refetchUnreadCount();
      } catch (error) {
        // Revert optimistic update on error
        refetchNotifications();
      }
    },
    [notifications, localMarkAsRead, refetchNotifications, refetchUnreadCount]
  );

  const markAllAsRead = useCallback(async () => {
    // Optimistic update
    localMarkAllAsRead();

    try {
      await apiMarkAllAsRead();
      refetchNotifications();
      refetchUnreadCount();
    } catch (error) {
      // Revert optimistic update on error
      refetchNotifications();
    }
  }, [localMarkAllAsRead, refetchNotifications, refetchUnreadCount]);

  const removeNotification = useCallback(
    async (id: string) => {
      // Optimistic update
      localRemoveNotification(id);

      try {
        await apiDeleteNotification(id);
        refetchNotifications();
        refetchUnreadCount();
      } catch (error) {
        // Revert optimistic update on error
        refetchNotifications();
      }
    },
    [localRemoveNotification, refetchNotifications, refetchUnreadCount]
  );

  const clearAll = useCallback(async () => {
    // Optimistic update
    localClearAll();

    try {
      await apiClearAll();
      refetchNotifications();
      refetchUnreadCount();
    } catch (error) {
      // Revert optimistic update on error
      refetchNotifications();
    }
  }, [localClearAll, refetchNotifications, refetchUnreadCount]);

  return {
    notifications,
    unreadCount,
    isLoading,
    lastSyncTime,
    addNotification, // Still local for real-time WebSocket notifications
    markAsRead,
    markAllAsRead,
    removeNotification,
    clearAll,
    refresh,
  };
}
