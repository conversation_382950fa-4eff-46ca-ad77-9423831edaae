'use client';

import React, { useState, useEffect } from 'react';
import Pagination from '@/components/common/pagination';
import dayjs from 'dayjs';
import {
  Loader2,
  Ellipsis,
  Search,
  MessageSquare,
  Mail,
  Phone,
  Video,
  MessageCircle,
  Instagram,
  Twitter,
  Facebook,
} from 'lucide-react';
import { StatusBadge } from '@/components/common/status-badge';
import { EmptyState, LoadingState } from '@/components/common/dataState';
import { Input } from '@/components/ui/input';
import DateRangeFilter from '@/components/common/date-range-filter';
import { GetPatientInteractions } from '@/api/crm/data';
import { PatientInteraction } from '@/components/crm/types';
import Details from './details';
import Create from './create';
import { useDebounce } from '@/hooks/useDebounce';

interface InteractionTableProps {
  openCreate: boolean;
  setOpenCreate: React.Dispatch<React.SetStateAction<boolean>>;
}

export default function InteractionTable({
  openCreate,
  setOpenCreate,
}: InteractionTableProps) {
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10);
  const [queryParam, setQueryParam] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [channelFilter, setChannelFilter] = useState('');
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);
  const [openDetails, setOpenDetails] = useState(false);
  const [selectedInteraction, setSelectedInteraction] =
    useState<PatientInteraction | null>(null);
  const debouncedSearchTerm = useDebounce(searchTerm, 500);

  const handleEventFromModal = (interaction: PatientInteraction) => {
    setSelectedInteraction(interaction);
    setOpenDetails(true);
  };

  useEffect(() => {
    let newQueryParam = '';

    if (debouncedSearchTerm) {
      newQueryParam = `search=${debouncedSearchTerm}`;
    }

    if (statusFilter) {
      newQueryParam = newQueryParam
        ? `${newQueryParam}&status=${statusFilter}`
        : `status=${statusFilter}`;
    }

    if (channelFilter) {
      newQueryParam = newQueryParam
        ? `${newQueryParam}&channel=${channelFilter}`
        : `channel=${channelFilter}`;
    }

    if (startDate) {
      const formattedStartDate = dayjs(startDate).format('YYYY-MM-DD');
      newQueryParam = newQueryParam
        ? `${newQueryParam}&startDate=${formattedStartDate}`
        : `startDate=${formattedStartDate}`;
    }

    if (endDate) {
      const formattedEndDate = dayjs(endDate).format('YYYY-MM-DD');
      newQueryParam = newQueryParam
        ? `${newQueryParam}&endDate=${formattedEndDate}`
        : `endDate=${formattedEndDate}`;
    }

    setCurrentPage(1); // Reset to first page on new search
    setQueryParam(newQueryParam);
  }, [debouncedSearchTerm, statusFilter, channelFilter, startDate, endDate]);

  const { interactions, interactionsLoading, mutate } = GetPatientInteractions(
    `?page=${currentPage}&limit=${pageSize}${queryParam ? `&${queryParam}` : ''}`
  );
  const interactionData = interactions?.data?.interactions || [];
  const totalPages = interactions?.data?.totalPages ?? 0;

  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  // Function to render the appropriate icon based on channel
  const getChannelIcon = (channel: string) => {
    switch (channel) {
      case 'whatsapp':
        return <MessageSquare className="w-4 h-4 text-green-500" />;
      case 'email':
        return <Mail className="w-4 h-4 text-blue-500" />;
      case 'sms':
        return <MessageCircle className="w-4 h-4 text-purple-500" />;
      case 'phone':
        return <Phone className="w-4 h-4 text-red-500" />;
      case 'video':
        return <Video className="w-4 h-4 text-orange-500" />;
      case 'instagram':
        return <Instagram className="w-4 h-4 text-pink-500" />;
      case 'twitter':
        return <Twitter className="w-4 h-4 text-blue-400" />;
      case 'facebook':
        return <Facebook className="w-4 h-4 text-blue-600" />;
      default:
        return <MessageSquare className="w-4 h-4 text-gray-500" />;
    }
  };

  return (
    <div className="w-full">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 gap-4">
        <div className="relative w-full sm:w-64">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search interactions..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
          <div className="flex gap-2 flex-wrap">
            <button
              onClick={() => setStatusFilter('')}
              className={`px-3 py-1 text-xs rounded-full ${
                statusFilter === ''
                  ? 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400'
                  : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
              }`}
            >
              All Status
            </button>
            <button
              onClick={() => setStatusFilter('pending')}
              className={`px-3 py-1 text-xs rounded-full ${
                statusFilter === 'pending'
                  ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400'
                  : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
              }`}
            >
              Pending
            </button>
            <button
              onClick={() => setStatusFilter('completed')}
              className={`px-3 py-1 text-xs rounded-full ${
                statusFilter === 'completed'
                  ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                  : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
              }`}
            >
              Completed
            </button>
          </div>
          <DateRangeFilter
            startDate={startDate}
            endDate={endDate}
            onStartDateChange={setStartDate}
            onEndDateChange={setEndDate}
            onClear={() => {
              setStartDate(null);
              setEndDate(null);
            }}
          />
        </div>
      </div>

      <div className="flex gap-2 mb-4 flex-wrap">
        <button
          onClick={() => setChannelFilter('')}
          className={`px-3 py-1 text-xs rounded-full ${
            channelFilter === ''
              ? 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400'
              : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
          }`}
        >
          All Channels
        </button>
        <button
          onClick={() => setChannelFilter('whatsapp')}
          className={`px-3 py-1 text-xs rounded-full ${
            channelFilter === 'whatsapp'
              ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
              : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
          }`}
        >
          WhatsApp
        </button>
        <button
          onClick={() => setChannelFilter('email')}
          className={`px-3 py-1 text-xs rounded-full ${
            channelFilter === 'email'
              ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400'
              : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
          }`}
        >
          Email
        </button>
        <button
          onClick={() => setChannelFilter('sms')}
          className={`px-3 py-1 text-xs rounded-full ${
            channelFilter === 'sms'
              ? 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400'
              : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
          }`}
        >
          SMS
        </button>
        <button
          onClick={() => setChannelFilter('phone')}
          className={`px-3 py-1 text-xs rounded-full ${
            channelFilter === 'phone'
              ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'
              : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
          }`}
        >
          Phone
        </button>
        <button
          onClick={() => setChannelFilter('social')}
          className={`px-3 py-1 text-xs rounded-full ${
            channelFilter === 'social'
              ? 'bg-pink-100 text-pink-800 dark:bg-pink-900/30 dark:text-pink-400'
              : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
          }`}
        >
          Social Media
        </button>
      </div>

      <div className="overflow-x-auto">
        <table className="w-full border-collapse">
          <thead>
            <tr className="bg-gray-100 dark:bg-[#1F1F23] text-xs text-gray-500 dark:text-gray-400">
              <th className="table-style">#</th>
              <th className="table-style">Date & Time</th>
              <th className="table-style">Patient</th>
              <th className="table-style">Channel</th>
              <th className="table-style">Subject</th>
              <th className="table-style">Direction</th>
              <th className="table-style">Status</th>
              <th className="table-style">Actions</th>
            </tr>
          </thead>
          <tbody className="relative overflow-x-auto whitespace-nowrap text-sm shadow-md">
            {interactionsLoading ? (
              <tr>
                <td colSpan={8} className="text-center py-8">
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900"></div>
                    <span className="ml-2">Loading interactions...</span>
                  </div>
                </td>
              </tr>
            ) : interactionData && interactionData.length > 0 ? (
              interactionData.map((interaction: any, index: number) => (
                <tr
                  className="text-xs text-[#062A55] dark:text-white"
                  key={interaction.id}
                >
                  <td className="table-style">
                    {currentPage === 1
                      ? index + 1
                      : (currentPage - 1) * pageSize + (index + 1)}
                  </td>
                  <td className="table-style">
                    {dayjs(interaction.createdAt).format('MMM D, YYYY h:mm A')}
                  </td>
                  <td className="table-style">{interaction.patientName}</td>
                  <td className="table-style">
                    <div className="flex items-center gap-2">
                      {getChannelIcon(interaction.channel)}
                      <span className="capitalize">{interaction.channel}</span>
                    </div>
                  </td>
                  <td className="table-style max-w-[200px] truncate">
                    {interaction.subject}
                  </td>
                  <td className="table-style">
                    <span
                      className={`px-2 py-1 rounded-full text-xs ${
                        interaction.direction === 'inbound'
                          ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400'
                          : 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                      }`}
                    >
                      {interaction.direction === 'inbound'
                        ? 'Inbound'
                        : 'Outbound'}
                    </span>
                  </td>
                  <td className="table-style">
                    <span
                      className={`px-2 py-1 rounded-full text-xs ${
                        interaction.status === 'completed'
                          ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                          : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400'
                      }`}
                    >
                      {interaction.status.charAt(0).toUpperCase() +
                        interaction.status.slice(1)}
                    </span>
                  </td>
                  <td className="table-style">
                    <Ellipsis
                      onClick={() => handleEventFromModal(interaction)}
                      className="w-4 h-4 cursor-pointer"
                    />
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={8} className="text-center py-8">
                  <div className="flex flex-col items-center justify-center text-gray-500">
                    <MessageSquare className="h-12 w-12 mb-2 opacity-50" />
                    <p className="text-lg font-medium">No interactions found</p>
                    <p className="text-sm">
                      {debouncedSearchTerm || startDate || endDate
                        ? 'Try adjusting your search or date filters'
                        : 'Patient interactions will appear here once they are recorded'}
                    </p>
                  </div>
                </td>
              </tr>
            )}
          </tbody>
        </table>
        <div className="mt-4">
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={handlePageChange}
          />
        </div>
      </div>

      {selectedInteraction && (
        <Details
          open={openDetails}
          setOpen={setOpenDetails}
          data={selectedInteraction}
          mutate={mutate}
        />
      )}

      <Create open={openCreate} setOpen={setOpenCreate} mutate={mutate} />
    </div>
  );
}
