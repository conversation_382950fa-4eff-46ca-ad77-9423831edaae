export interface UserProps {
  id?: string;
  fullName: string;
  locked: boolean;
  staffId: string;
  staffCode?: string;
  roles: any;
  createdAt: Date;
  email: string;
  role: string;
  lastLogin: string;
  isActive: boolean;
  dateResetPasswordRequest: Date;
  dateResetPassword: Date;
  phoneNumber?: string;
  type?: string;
  location?: {
    name: string;
    region: string;
  };
  department?: {
    name: string;
  };
  unit?: {
    name: string;
  };
  unitId?: string;
  isDoctor?: boolean;
  isConsultant?: boolean;
  isVisitingConsultant?: boolean;
  specialty?: string;
  referralCode?: {
    code: string;
  };
  codeUsage?: number;
  total?: number;
  wallet?: number;
  creditLimit?: number;
  mealVoucher?: number;
  monthlyCreditUsed?: number;
  doctorProfile?: any;
  account: any;
}

export interface MenuItem {
  label: string;
  href: string;
  icon?: React.ReactNode;
}
