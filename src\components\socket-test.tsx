'use client';

import { useWebSocket } from '@/hooks/useWebSocket';
import { useNotifications } from '@/hooks/useNotifications';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { SOCKET_EVENTS } from '@/lib/socket-config';

export function SocketTest() {
  const { isConnected, connectionStatus, emit } = useWebSocket();
  const { unreadCount } = useNotifications();

  const testNotification = () => {
    // Test emitting a notification event to the server
    emit(SOCKET_EVENTS.TEST_NOTIFICATION, {
      title: 'Test Notification',
      message: 'This is a test notification from the client',
      link: '/dashboard',
    });
  };

  return (
    <div className="p-4 border rounded-lg space-y-4">
      <h3 className="text-lg font-semibold">Socket.IO Status</h3>

      <div className="flex items-center gap-2">
        <span>Connection Status:</span>
        <Badge
          variant={
            connectionStatus === 'connected'
              ? 'default'
              : connectionStatus === 'offline'
                ? 'secondary'
                : 'destructive'
          }
        >
          {connectionStatus === 'offline' ? 'Offline Mode' : connectionStatus}
        </Badge>
      </div>

      {connectionStatus === 'offline' && (
        <div className="text-sm text-muted-foreground">
          Real-time features are disabled. The Socket.IO server is not
          available.
        </div>
      )}

      <div className="flex items-center gap-2">
        <span>Unread Notifications:</span>
        <Badge variant="secondary">{unreadCount}</Badge>
      </div>

      <Button
        onClick={testNotification}
        disabled={!isConnected}
        variant="outline"
      >
        Test Notification
      </Button>
    </div>
  );
}
