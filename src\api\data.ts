import useSWR, { mutate } from 'swr';

interface APiResponse {
  message: string;
  data: Data;
}

interface Category {
  id: number;
  createdAt: Date;
  name: string;
  slug: string;
  createdBy: string;
  updatedBy: string | null;
  isActive: boolean;
  packagesCount: number;
}

interface Data {
  categories: Category[];
  totalPages: number;
  totalCount: number;
}

interface CategoryResponse {
  category: APiResponse | undefined;
  categoryError: any;
  categoryLoading: boolean;
  mutate: () => void;
}

export const GetCategories = (params: string): CategoryResponse => {
  const qs = new URLSearchParams(params);

  const { data, error, isLoading, mutate } = useSWR<APiResponse>(
    `/package/list-category?${qs.toString()}`
  );

  return {
    category: data,
    categoryError: error,
    categoryLoading: isLoading,
    mutate: mutate,
  };
};

//Get tests
export const GetPackageTest = () => {
  const { data, isLoading, error } = useSWR('/package/list-test');

  return {
    test: data,
    isLoading: isLoading,
    error: error,
  };
};

export const GetInvestigation = (params: string) => {
  const qs = new URLSearchParams(params);

  const { data, error, isLoading, mutate } = useSWR(
    `/package//list-investigation-paginated?${qs.toString()}`
  );

  return {
    investigation: data,
    investigationError: error,
    investigationLoading: isLoading,
    mutate: mutate,
  };
};

//Get public categories
export const GetPublicCategories = () => {
  const { data, isLoading, error } = useSWR('/package/list-public-category');

  return {
    category: data,
    isLoading: isLoading,
    error: error,
  };
};

//Get locations
export const GetLocations = () => {
  const { data, isLoading, error, mutate } = useSWR('/location/list');

  return {
    locations: data,
    isLoading: isLoading,
    error: error,
    mutate: mutate,
  };
};

export const GetPackages = (params: string) => {
  const qs = new URLSearchParams(params);

  const { data, error, isLoading, mutate } = useSWR(
    `/package/list-package-admin?${qs.toString()}`
  );

  return {
    packages: data,
    packageLoading: isLoading,
    mutate: mutate,
  };
};

//Get package by slug
export const GetPackageBySlug = (slug: string) => {
  const { data, error, isLoading, mutate } = useSWR(
    `/package/single-package/${slug}`
  );

  return {
    packageData: data,
    packageLoading: isLoading,
    mutate: mutate,
  };
};

//Get package by slug - Admin
export const GetPackageAdminBySlug = (slug: string) => {
  const { data, error, isLoading, mutate } = useSWR(
    `/package/single-package-admin/${slug}`
  );

  return {
    packageData: data,
    packageLoading: isLoading,
    mutate: mutate,
  };
};

export const GetCategoryById = (categoryId: number) => {
  const { data } = useSWR(`/package/get-category-id/${categoryId}`);

  return {
    category: data,
  };
};

export const GetPackageBookings = (params: string) => {
  const qs = new URLSearchParams(params);

  const { data, error, isLoading, mutate } = useSWR(
    `/booking/list-all-package-booking?${qs.toString()}`
  );

  return {
    bookings: data,
    bookingLoading: isLoading,
    mutate: mutate,
  };
};

export const GetTransactions = (params: string) => {
  const qs = new URLSearchParams(params);

  const { data, mutate, isLoading } = useSWR(
    `/transaction/list-all-transaction?${qs.toString()}`
  );

  return {
    transactions: data,
    transactionLoading: isLoading,
    mutate: mutate,
  };
};

export const GetStaffTransactions = (params: string) => {
  const qs = new URLSearchParams(params);

  const { data, mutate, isLoading } = useSWR(
    `/transaction/list-staff-transaction?${qs.toString()}`
  );

  return {
    transactions: data,
    transactionLoading: isLoading,
    mutate: mutate,
  };
};

export const GetBookingAnalytics = (params: string) => {
  const qs = new URLSearchParams(params);

  const { data, error, isLoading } = useSWR(
    `/analytic/booking-graph?${qs.toString()}`
  );

  return {
    bookingAnalytics: data,
    bookingAnalyticsLoading: isLoading,
    bookingAnalyticsError: error,
  };
};

export const GetBookingStats = () => {
  const { data, isLoading, error } = useSWR('/analytic/booking-stats');

  return {
    stats: data,
    statsLoading: isLoading,
    statsError: error,
  };
};

//Get all discount usage
export const GetDiscountRecords = (params: string) => {
  const qs = new URLSearchParams(params);

  const { data, error, isLoading } = useSWR(
    `/discount/list-discount-records?${qs.toString()}`
  );

  return {
    discountUsage: data,
    discountUsageLoading: isLoading,
    discountUsageError: error,
  };
};

//Get all discount codes
export const GetDiscounts = (params: string) => {
  const qs = new URLSearchParams(params);

  const { data, error, isLoading, mutate } = useSWR(
    `/discount/list-discounts?${qs.toString()}`
  );

  return {
    discount: data,
    discountLoading: isLoading,
    discountError: error,
    mutate: mutate,
  };
};

export const GetPackagePrices = () => {
  const { data, isLoading } = useSWR('/package/list-location-price');

  return {
    packagePrices: data,
    packagePriceLoading: isLoading,
  };
};

//Get all discount usage
export const GetFeedbackList = (params: string) => {
  const qs = new URLSearchParams(params);

  const { data, error, mutate, isLoading } = useSWR(
    `/feedback/list?${qs.toString()}`
  );

  return {
    feedbacks: data,
    feedbacksLoading: isLoading,
    feedbackError: error,
    mutate: mutate,
  };
};
