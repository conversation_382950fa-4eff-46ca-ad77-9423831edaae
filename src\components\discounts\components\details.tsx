'use client';

import React, { useState, useEffect } from 'react';
import { Modal } from '@/components/common/modal';
import { ModalProps } from '@/components/types';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { myApi } from '@/api/fetcher';
import { toast } from 'sonner';
import { Loader2, Edit2, Check, X, CalendarIcon } from 'lucide-react';
import dayjs from 'dayjs';
import { numberFormat } from '@/lib/utils';
import { StatusBadge } from '@/components/common/status-badge';
import { MultiSelect } from '@/components/common/multi-select';
import { GetPackagePrices } from '@/api/data';
import { InputCalendar } from '@/components/common/form';

interface DetailsProps extends ModalProps {
  data: any;
}

interface Price {
  id: string;
  name: string;
}

const Details: React.FC<DetailsProps> = ({ setOpen, open, mutate, data }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [editingField, setEditingField] = useState<string | null>(null);
  const [selectedPackages, setSelectedPackages] = useState<string[]>([]);

  const [editValues, setEditValues] = useState({
    endDate: data?.endDate ? new Date(data.endDate) : null,
    isActive: data?.isActive ?? false,
  });

  useEffect(() => {
    if (data) {
      setEditValues({
        endDate: data.endDate ? new Date(data.endDate) : null,
        isActive: data.isActive ?? false,
      });
      const initialPackageIds = Array.from(
        new Set(
          data.packageLocationPrice?.map((plp: any) => plp.package.id) || []
        )
      );
      setSelectedPackages(initialPackageIds as string[]);
      setEditingField(null);
    }
  }, [data, open]);

  if (!data) return null;

  const handleSaveField = async (field: string) => {
    if (!data?.id) {
      toast.error('Discount ID is missing');
      return;
    }

    try {
      setIsLoading(true);
      const updateData: any = { id: data.id };
      if (field === 'endDate') {
        updateData.endDate = editValues.endDate
          ? dayjs(editValues.endDate).toISOString()
          : null;
      } else if (field === 'packages') {
        updateData.packages = selectedPackages;
      } else {
        updateData[field] = editValues[field as keyof typeof editValues];
      }

      await myApi.patch('/discount/update-discount', updateData);

      toast.success('Discount updated successfully');
      setEditingField(null);
      setOpen(false);
      if (mutate) {
        mutate();
      }
    } catch (error) {
      console.log('Failed to update discount');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancelEdit = (field: string) => {
    setEditingField(null);
    if (field === 'endDate') {
      setEditValues((prev) => ({
        ...prev,
        endDate: data.endDate ? new Date(data.endDate) : null,
      }));
    }
    if (field === 'isActive') {
      setEditValues((prev) => ({
        ...prev,
        isActive: data.isActive ?? false,
      }));
    }
    if (field === 'packages') {
      const initialPackageIds = Array.from(
        new Set(
          data.packageLocationPrice?.map((plp: any) => plp.package.id) || []
        )
      );
      setSelectedPackages(initialPackageIds as string[]);
    }
  };

  const renderEditableStatus = () => {
    const isEditing = editingField === 'isActive';
    return (
      <div>
        <div className="flex items-center">
          <p className="text-sm font-medium">Status</p>
          {!isEditing && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setEditingField('isActive')}
              className="h-4 w-4 ml-4 p-0"
            >
              <Edit2 className="w-3 h-3" />
            </Button>
          )}
        </div>
        {isEditing ? (
          <div className="flex items-center gap-2 mt-1">
            <Switch
              checked={editValues.isActive}
              onCheckedChange={(checked) =>
                setEditValues((prev) => ({ ...prev, isActive: checked }))
              }
            />
            <span className="text-sm">
              {editValues.isActive ? 'Active' : 'Inactive'}
            </span>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleSaveField('isActive')}
              disabled={isLoading}
              className="h-6 w-6 p-0"
            >
              {isLoading && editingField === 'isActive' ? (
                <Loader2 className="w-3 h-3 animate-spin" />
              ) : (
                <Check className="w-3 h-3" />
              )}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleCancelEdit('isActive')}
              className="h-6 w-6 p-0"
            >
              <X className="w-3 h-3" />
            </Button>
          </div>
        ) : (
          <div className="mt-1">
            <StatusBadge status={data.isActive ? 'active' : 'inactive'} />
          </div>
        )}
      </div>
    );
  };

  const renderEditableEndDate = () => {
    const isEditing = editingField === 'endDate';
    return (
      <div>
        <div className="flex items-center">
          <p className="text-sm font-medium">End Date</p>
          {!isEditing && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setEditingField('endDate')}
              className="h-4 w-4 ml-4 p-0"
            >
              <Edit2 className="w-3 h-3" />
            </Button>
          )}
        </div>
        {isEditing ? (
          <div className="flex items-center gap-2 mt-1">
            <InputCalendar
              label=""
              value={editValues.endDate}
              onSelect={(date) =>
                setEditValues((prev) => ({
                  ...prev,
                  endDate: date || null,
                }))
              }
              minDate={data.startDate ? new Date(data.startDate) : undefined}
              className="w-[240px] h-8"
            />
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleSaveField('endDate')}
              disabled={isLoading}
              className="h-6 w-6 p-0"
            >
              {isLoading && editingField === 'endDate' ? (
                <Loader2 className="w-3 h-3 animate-spin" />
              ) : (
                <Check className="w-3 h-3" />
              )}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleCancelEdit('endDate')}
              className="h-6 w-6 p-0"
            >
              <X className="w-3 h-3" />
            </Button>
          </div>
        ) : (
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
            {data.endDate ? dayjs(data.endDate).format('MMMM D, YYYY') : 'N/A'}
          </p>
        )}
      </div>
    );
  };

  const { packagePrices } = GetPackagePrices();
  const packageOptions = packagePrices?.data ?? [];

  const renderEditablePackages = () => {
    const isEditing = editingField === 'packages';

    return (
      <div>
        <div className="flex items-center mb-3">
          <h3 className="text-lg font-semibold text-foreground">
            Applied to Packages ({data.packagePricesCount})
          </h3>
          {!isEditing && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setEditingField('packages')}
              className="h-6 w-6 ml-4 p-0"
            >
              <Edit2 className="w-4 h-4" />
            </Button>
          )}
        </div>

        {isEditing ? (
          <div className="space-y-4">
            <MultiSelect
              options={packageOptions}
              selected={selectedPackages}
              onChange={setSelectedPackages}
              placeholder="Select package prices"
              valueField="id"
              labelField="name"
              className="w-full"
              renderOption={(price, isSelected) => (
                <div className="flex items-center">
                  <div className="mr-2 flex h-4 w-4 items-center justify-center">
                    {isSelected ? <Check className="h-4 w-4" /> : null}
                  </div>
                  <span>{(price as unknown as Price).name}</span>
                </div>
              )}
            />
            <div className="flex items-center gap-2">
              <Button
                size="sm"
                onClick={() => handleSaveField('packages')}
                disabled={isLoading}
              >
                {isLoading && editingField === 'packages' ? (
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                ) : null}
                Save Packages
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleCancelEdit('packages')}
              >
                Cancel
              </Button>
            </div>
          </div>
        ) : data.packageLocationPrice &&
          data.packageLocationPrice.length > 0 ? (
          <div className="max-h-40 overflow-auto space-y-2 pr-2">
            <table className="border w-full text-xs">
              <thead>
                <tr>
                  <th className="px-2 py-1 text-left font-semibold">Package</th>
                  <th className="px-2 py-1 text-left font-medium">Location</th>
                  <th className="px-2 py-1 text-left font-medium">Amount</th>
                </tr>
              </thead>
              <tbody>
                {data.packageLocationPrice.map((item: any) => (
                  <tr key={item.id}>
                    <td className="px-2 py-1 border truncate overflow-hidden whitespace-nowrap">
                      {item.package?.name || 'N/A'}
                    </td>
                    <td className="px-2 border">
                      {item.location?.name || 'N/A'}
                    </td>
                    <td className="px-2 border">
                      ₦{Number(item.amount).toLocaleString()}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <p className="text-sm text-gray-500">Not applied to any packages.</p>
        )}
      </div>
    );
  };

  return (
    <Modal
      open={open}
      setOpen={setOpen}
      title="Discount Code Details"
      description={`Details for code: ${data.modifierCode}`}
      size="lg"
    >
      <div className="space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-foreground mb-3">
              Discount Information
            </h3>
            <div>
              <p className="text-sm font-medium">Code</p>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {data.modifierCode}
              </p>
            </div>
            <div>
              <p className="text-sm font-medium">Description</p>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {data.description || 'N/A'}
              </p>
            </div>
            <div>
              <p className="text-sm font-medium">Discount Value</p>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {data.percentage
                  ? `${data.percentage}%`
                  : numberFormat(data.amount)}
              </p>
            </div>
            <div>
              <p className="text-sm font-medium">Date Created</p>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {dayjs(data.createdAt).format('MMMM D, YYYY')}
              </p>
            </div>
          </div>
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-foreground mb-3">
              Validity & Status
            </h3>
            <div>
              <p className="text-sm font-medium">Start Date</p>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {dayjs(data.startDate).format('MMMM D, YYYY')}
              </p>
            </div>
            {renderEditableEndDate()}
            {renderEditableStatus()}
          </div>
        </div>
        {renderEditablePackages()}
      </div>
      <div className="flex justify-end gap-2 mt-6 pt-4 border-t">
        <Button variant="outline" onClick={() => setOpen(false)}>
          Close
        </Button>
      </div>
    </Modal>
  );
};

export default Details;
