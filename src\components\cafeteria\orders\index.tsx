'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { GetAllOrders, GetGeneralOrders } from '@/api/cafeteria/menu';
import { currencyFormat } from '@/lib/utils';
import DateRangeFilter from '@/components/common/date-range-filter';
import MonthYearFilter from '@/components/common/month-year-filter';
import dayjs from 'dayjs';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { ShoppingCart, Search, Eye } from 'lucide-react';
import { useSearchAndPagination } from '@/hooks/useSearchAndPagination';
import Pagination from '@/components/common/pagination';
import { hasPermission, PERMISSIONS } from '@/lib/types/permissions';
import OrderStats from './components/OrderStats';
import OrderSpecial from './components/OrderSpecial';
import OrderDetailsModal from './components/OrderDetailsModal';

interface OrdersManagementProps {
  orderType: 'general' | 'staff' | 'special';
}

export default function OrdersManagement({ orderType }: OrdersManagementProps) {
  const [startDate, setStartDate] = useState<Date | undefined>(undefined);
  const [endDate, setEndDate] = useState<Date | undefined>(undefined);
  const [viewType, setViewType] = useState<'my' | 'all'>('my');
  const [selectedOrder, setSelectedOrder] = useState<any>(null);
  const [orderDetailsOpen, setOrderDetailsOpen] = useState(false);
  const [showOrderSpecial, setShowOrderSpecial] = useState(false);
  const [selectedMonth, setSelectedMonth] = useState<string>('');
  const [selectedYear, setSelectedYear] = useState<string>('');
  const [selectedPayment, setSelectedPayment] = useState<string>('');
  const [selectedSalesType, setSelectedSalesType] = useState<string>('');

  const canManageOrders = hasPermission(PERMISSIONS.CAFETERIA_ORDERS_MANAGE);
  const canAccessPOS = hasPermission(PERMISSIONS.CAFETERIA_POS_ACCESS);

  const {
    searchTerm,
    debouncedSearchTerm,
    handleSearchChange,
    currentPage,
    pageSize,
    handlePageChange,
    queryParam,
    setQueryParam,
  } = useSearchAndPagination({ initialPageSize: 15 });

  useEffect(() => {
    if (canManageOrders) {
      setViewType('all');
    }
  }, [canManageOrders]);

  useEffect(() => {
    let params = [];

    if (debouncedSearchTerm) {
      params.push(`search=${debouncedSearchTerm}`);
    }

    if (selectedMonth && selectedYear) {
      params.push(`month=${selectedMonth}&year=${selectedYear}`);
    }

    if (startDate) {
      const formattedStartDate = dayjs(startDate).format('YYYY-MM-DD');
      params.push(`startDate=${formattedStartDate}`);
    }

    if (endDate) {
      const formattedEndDate = dayjs(endDate).format('YYYY-MM-DD');
      params.push(`endDate=${formattedEndDate}`);
    }

    if (selectedPayment && selectedPayment !== 'all') {
      params.push(`paymentType=${selectedPayment}`);
    }
    if (selectedSalesType && selectedSalesType !== 'all') {
      params.push(`saleType=${selectedSalesType}`);
    }

    // No need to add staff parameter since APIs are now separated
    setQueryParam(params.join('&'));
  }, [
    debouncedSearchTerm,
    selectedMonth,
    selectedYear,
    startDate,
    endDate,
    selectedPayment,
    selectedSalesType,
    setQueryParam,
  ]);

  // Conditionally use different orders APIs based on viewType
  const {
    orders: myOrders,
    orderLoading: myOrderLoading,
    mutate: mutateMyOrders,
  } = GetAllOrders(`?page=${currentPage}&limit=${pageSize}&${queryParam}`);
  const {
    orders: generalOrders,
    orderLoading: generalOrderLoading,
    mutate: mutateGeneralOrders,
  } = canManageOrders
    ? GetGeneralOrders(`?page=${currentPage}&limit=${pageSize}&${queryParam}`)
    : { orders: null, orderLoading: false, mutate: () => {} };

  // Use appropriate orders data based on viewType
  const orders = viewType === 'all' ? generalOrders : myOrders;
  const orderLoading =
    viewType === 'all' ? generalOrderLoading : myOrderLoading;
  const orderData = orders?.data?.orders || [];
  const totalPages = orders?.data?.totalPages ?? 0;

  // Extract stats from orders response
  const stats = {
    overallOrders: orders?.data?.overallOrders || 0,
    totalOrders: orders?.data?.totalOrders || 0,
    totalAmount: orders?.data?.totalAmount || 0,
  };

  const orderStatsLoading = orderLoading;

  const handleDateRangeChange = (
    start: Date | undefined,
    end: Date | undefined
  ) => {
    setStartDate(start);
    setEndDate(end);
  };

  const handleMonthYearChange = (month: string, year: string) => {
    setSelectedMonth(month);
    setSelectedYear(year);
  };

  const handleViewOrderDetails = (order: any) => {
    setSelectedOrder(order);
    setOrderDetailsOpen(true);
  };

  if (showOrderSpecial) {
    return (
      <OrderSpecial
        onBack={() => setShowOrderSpecial(false)}
        mutate={() => {
          if (viewType === 'my') {
            mutateMyOrders();
          } else {
            mutateGeneralOrders();
          }
        }}
      />
    );
  }

  return (
    <div className="space-y-4">
      <Button onClick={() => setShowOrderSpecial(true)} size="sm">
        Order Special
      </Button>
      <div className="flex flex-wrap gap-3 items-center">
        <MonthYearFilter
          onFilterChange={handleMonthYearChange}
          className="flex gap-2"
        />
        <DateRangeFilter
          onDateRangeChange={handleDateRangeChange}
          className="w-[250px]"
        />
        <Select value={selectedPayment} onValueChange={setSelectedPayment}>
          <SelectTrigger className="w-[150px]">
            <SelectValue placeholder="Payment Type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Payments</SelectItem>
            <SelectItem value="credit">Credit</SelectItem>
            <SelectItem value="card">Card</SelectItem>
            <SelectItem value="transfer">Transfer</SelectItem>
            <SelectItem value="wallet">Wallet</SelectItem>
            <SelectItem value="voucher">Meal Voucher</SelectItem>
          </SelectContent>
        </Select>
        {viewType === 'all' && (
          <Select
            value={selectedSalesType}
            onValueChange={setSelectedSalesType}
          >
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="Sales Type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All</SelectItem>
              <SelectItem value="staff">Staff</SelectItem>
              <SelectItem value="general">General</SelectItem>
            </SelectContent>
          </Select>
        )}
        <div className="relative w-64">
          <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <Search className="w-4 h-4 text-gray-500" />
          </div>
          <Input
            type="text"
            placeholder="Search ..."
            className="pl-10 pr-4 py-2 w-full"
            value={searchTerm}
            onChange={handleSearchChange}
          />
        </div>
        {canManageOrders && (
          <div className="flex gap-2">
            <Button
              variant={viewType === 'my' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewType('my')}
            >
              My Orders
            </Button>
            <Button
              variant={viewType === 'all' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewType('all')}
            >
              All Orders
            </Button>
          </div>
        )}
      </div>
      <OrderStats
        stats={stats}
        orderStatsLoading={orderStatsLoading}
        viewType={viewType}
        startDate={startDate}
        endDate={endDate}
      />

      <div className="border rounded-md">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>S/N</TableHead>
              <TableHead>Date</TableHead>
              <TableHead>Order No</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Sales Type</TableHead>
              <TableHead>Payment</TableHead>
              <TableHead>Total Amount</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {orderLoading ? (
              <TableRow>
                <TableCell colSpan={8} className="text-center py-8">
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900"></div>
                    <span className="ml-2">Loading orders...</span>
                  </div>
                </TableCell>
              </TableRow>
            ) : orderData && orderData.length > 0 ? (
              orderData.map((order: any, index: number) => (
                <TableRow key={order.id || index}>
                  <TableCell>
                    {currentPage === 1
                      ? index + 1
                      : (currentPage - 1) * pageSize + (index + 1)}
                  </TableCell>
                  <TableCell>
                    {dayjs(order.createdAt).format('YYYY-MM-DD')}
                  </TableCell>
                  <TableCell>{order.orderNumber || 'N/A'}</TableCell>
                  <TableCell>{order.orderType || 'N/A'}</TableCell>
                  <TableCell>
                    {order?.saleType?.toUpperCase() || 'N/A'}
                  </TableCell>
                  <TableCell>{order.paymentType || 'N/A'}</TableCell>
                  <TableCell>
                    {currencyFormat(order.totalAmount || 0)}
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleViewOrderDetails(order)}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={8} className="text-center py-8">
                  <div className="flex flex-col items-center justify-center text-gray-500">
                    <ShoppingCart className="h-12 w-12 mb-2 opacity-50" />
                    <p className="text-lg font-medium">No orders found</p>
                    <p className="text-sm">
                      {debouncedSearchTerm || startDate || endDate
                        ? 'Try adjusting your search or date filters'
                        : 'Orders will appear here once they are created'}
                    </p>
                  </div>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center mt-4">
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={handlePageChange}
          />
        </div>
      )}

      <OrderDetailsModal
        open={orderDetailsOpen}
        setOpen={setOrderDetailsOpen}
        selectedOrder={selectedOrder}
      />
    </div>
  );
}
