import { z } from 'zod';
import { isToday, addMinutes } from 'date-fns';

export const SpecialOrderFormSchema = z.object({
  neededDate: z
    .string({
      required_error: 'Please select a date and time.',
    })
    .min(1, 'Please select a date and time.')
    .refine(
      (val) =>
        new Date(val).getHours() !== 0 || new Date(val).getMinutes() !== 0,
      {
        message: 'Please select a valid time.',
      }
    )
    .refine(
      (val) => {
        const selectedDate = new Date(val);
        // If the selected date is not today, it's valid.
        if (!isToday(selectedDate)) {
          return true;
        }
        // If it is today, it must be at least 10 minutes in the future.
        const nowPlus10 = addMinutes(new Date(), 10);
        return selectedDate >= nowPlus10;
      },
      {
        message:
          'For same-day orders, time must be at least 10 minutes in the future.',
      }
    ),
  purpose: z.string().min(1, 'Purpose is required.').trim(),
});

export type SpecialOrderFormValues = z.infer<typeof SpecialOrderFormSchema>;
