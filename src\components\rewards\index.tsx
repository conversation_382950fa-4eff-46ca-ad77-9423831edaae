'use client';

import { useState } from 'react';
import { Gem } from 'lucide-react';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import RewardsTable from './components/data-table';
import { hasPermission, PERMISSIONS } from '@/lib/types/permissions';
import MealVoucherModal from './components/meal-voucher-modal';

export default function RewardsPage() {
  const [open, setOpen] = useState(false);
  const [showMealVoucherModal, setShowMealVoucherModal] = useState(false);
  const permitCreate = hasPermission(PERMISSIONS.REWARD_CREATE);
  const canCreateTransactions = hasPermission(PERMISSIONS.TRANSACTION_CREATE);

  return (
    <>
      <h2 className="text-lg font-bold text-gray-900 dark:text-white mb-4 text-left flex items-center gap-2">
        <Gem className="w-5 h-5 text-zinc-900 dark:text-zinc-50" />
        Rewards Management
      </h2>
      <div className="flex gap-2">
        {permitCreate && (
          <Button
            size="sm"
            className="cursor-pointer"
            onClick={() => setOpen(true)}
          >
            Add Reward
          </Button>
        )}
        {canCreateTransactions && (
          <Button
            className="cursor-pointer"
            variant="secondary"
            size="sm"
            onClick={() => setShowMealVoucherModal(true)}
          >
            Meal Voucher
          </Button>
        )}
      </div>
      <div className="mt-4 bg-white dark:bg-[#0F0F12] rounded-xl sm:p-4 flex flex-col items-start justify-start sm:border border-gray-200 dark:border-[#1F1F23]">
        <RewardsTable openCreate={open} setOpenCreate={setOpen} />
      </div>
      {showMealVoucherModal && (
        <MealVoucherModal
          open={showMealVoucherModal}
          setOpen={setShowMealVoucherModal}
        />
      )}
    </>
  );
}
