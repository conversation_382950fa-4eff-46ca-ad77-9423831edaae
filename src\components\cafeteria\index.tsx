'use client';

import { useState, useMemo, useEffect } from 'react';
import {
  Coffee,
  Package,
  MenuSquare,
  ShoppingCart,
  Calendar,
} from 'lucide-react';
import { hasPermission, PERMISSIONS } from '@/lib/types/permissions';
import { Tabs, Ta<PERSON>List, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import InventoryManagement from './inventory';
import MenuManagement from './menu';
import OrdersManagement from './orders';
import SalesManagement from './sales';

export default function CafeteriaContent() {
  const [orderType, setOrderType] = useState<'general' | 'staff' | 'special'>(
    'staff'
  );

  const canViewCafeteria = hasPermission(PERMISSIONS.CAFETERIA_VIEW);
  const canManageInventory = hasPermission(
    PERMISSIONS.CAFETERIA_INVENTORY_MANAGE
  );
  const canManageMenu = hasPermission(PERMISSIONS.CAFETERIA_MENU_MANAGE);
  const canScheduleMenu = hasPermission(PERMISSIONS.CAFETERIA_MENU_SCHEDULE);
  const canManageOrders = hasPermission(PERMISSIONS.CAFETERIA_ORDERS_MANAGE);
  const canAccessPOS = hasPermission(PERMISSIONS.CAFETERIA_POS_ACCESS);

  const defaultTab = useMemo(() => {
    return 'orders';
  }, []);

  const [activeTab, setActiveTab] = useState(defaultTab);

  const availableTabsCount = useMemo(() => {
    return [
      canManageInventory,
      canManageMenu,
      canScheduleMenu,
      true,
      canAccessPOS,
    ].filter(Boolean).length;
  }, [canManageInventory, canManageMenu, canScheduleMenu, canAccessPOS]);

  const getTabsClass = () => {
    const baseClass = 'flex overflow-x-auto scrollbar-hide';
    const responsiveClass = availableTabsCount > 2 ? 'lg:grid' : 'sm:grid';

    switch (availableTabsCount) {
      case 1:
        return `${baseClass} ${responsiveClass} sm:grid-cols-1`;
      case 2:
        return `${baseClass} ${responsiveClass} sm:grid-cols-2`;
      case 3:
        return `${baseClass} ${responsiveClass} md:grid-cols-3`;
      case 4:
        return `${baseClass} ${responsiveClass} md:grid-cols-1 lg:grid-cols-4`;
      default:
        return `${baseClass} ${responsiveClass} sm:grid-cols-1`;
    }
  };

  useEffect(() => {
    setActiveTab(defaultTab);
  }, [defaultTab]);

  if (!canViewCafeteria) {
    return (
      <div className="flex items-center justify-center h-full">
        You don't have permission to view the cafeteria management.
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <h2 className="text-lg font-bold text-gray-900 dark:text-white mb-4 text-left flex items-center gap-2">
        <Coffee className="w-5 h-5 text-zinc-900 dark:text-zinc-50" />
        Cafeteria Management
      </h2>

      {availableTabsCount === 1 ? (
        <div className="space-y-4 mt-0 animate-in fade-in-50 duration-300">
          <h3 className="text-md font-semibold text-gray-800 dark:text-gray-200 flex items-center gap-2">
            <ShoppingCart className="w-4 h-4" style={{ color: '#BD9A3D' }} />
            Orders Management
          </h3>
          <div>
            <OrdersManagement orderType={orderType} />
          </div>
        </div>
      ) : (
        <Tabs
          value={activeTab}
          onValueChange={setActiveTab}
          className="w-full animate-in fade-in-50"
        >
          <TabsList className={`${getTabsClass()} gap-2 mb-6 w-full`}>
            {canManageInventory && (
              <TabsTrigger
                value="inventory"
                className="flex items-center gap-1 sm:gap-2 text-xs sm:text-sm px-3 py-2 data-[state=active]:bg-background data-[state=active]:text-primary whitespace-nowrap flex-shrink-0 min-w-fit"
                disabled={!canManageInventory}
              >
                <Package
                  className="w-3 h-3 sm:w-4 sm:h-4 flex-shrink-0"
                  style={{
                    color: activeTab === 'inventory' ? '#BD9A3D' : 'inherit',
                  }}
                />
                <span className="hidden sm:inline">Inventory Management</span>
                <span className="sm:hidden">Inventory</span>
              </TabsTrigger>
            )}
            {canManageMenu && (
              <TabsTrigger
                value="menu"
                className="flex items-center gap-1 sm:gap-2 text-xs sm:text-sm px-3 py-2 data-[state=active]:bg-background data-[state=active]:text-primary whitespace-nowrap flex-shrink-0 min-w-fit"
                disabled={!canManageMenu}
              >
                <MenuSquare
                  className="w-3 h-3 sm:w-4 sm:h-4 flex-shrink-0"
                  style={{
                    color: activeTab === 'menu' ? '#BD9A3D' : 'inherit',
                  }}
                />
                <span className="hidden sm:inline">Menu Management</span>
                <span className="sm:hidden">Menu</span>
              </TabsTrigger>
            )}
            {canScheduleMenu && (
              <TabsTrigger
                value="scheduler"
                className="flex items-center gap-1 sm:gap-2 text-xs sm:text-sm px-3 py-2 data-[state=active]:bg-background data-[state=active]:text-primary whitespace-nowrap flex-shrink-0 min-w-fit"
                disabled={!canScheduleMenu}
              >
                <Calendar
                  className="w-3 h-3 sm:w-4 sm:h-4 flex-shrink-0"
                  style={{
                    color: activeTab === 'scheduler' ? '#BD9A3D' : 'inherit',
                  }}
                />
                <span className="hidden sm:inline">Menu Scheduler</span>
                <span className="sm:hidden">Scheduler</span>
              </TabsTrigger>
            )}

            <TabsTrigger
              value="orders"
              className="flex items-center gap-1 sm:gap-2 text-xs sm:text-sm px-3 py-2 data-[state=active]:bg-background data-[state=active]:text-primary whitespace-nowrap flex-shrink-0 min-w-fit"
            >
              <ShoppingCart
                className="w-3 h-3 sm:w-4 sm:h-4 flex-shrink-0"
                style={{
                  color: activeTab === 'orders' ? '#BD9A3D' : 'inherit',
                }}
              />
              <span className="hidden sm:inline">Orders Management</span>
              <span className="sm:hidden">Orders</span>
            </TabsTrigger>
            {canAccessPOS && (
              <TabsTrigger
                value="sales"
                className="flex items-center gap-1 sm:gap-2 text-xs sm:text-sm px-3 py-2 data-[state=active]:bg-background data-[state=active]:text-primary whitespace-nowrap flex-shrink-0 min-w-fit"
                disabled={!canAccessPOS}
              >
                <span
                  className="text-sm sm:text-base flex-shrink-0"
                  style={{
                    color: activeTab === 'sales' ? '#BD9A3D' : 'inherit',
                  }}
                >
                  &#8358;
                </span>
                <span className="hidden sm:inline">Sales Management</span>
                <span className="sm:hidden">Sales</span>
              </TabsTrigger>
            )}
          </TabsList>

          {/* Inventory Management Tab */}
          <TabsContent
            value="inventory"
            className="mt-0 animate-in fade-in-50 duration-300"
          >
            <div className="space-y-4">
              <h3 className="text-md font-semibold text-gray-800 dark:text-gray-200 flex items-center gap-2">
                <Package className="w-4 h-4" style={{ color: '#BD9A3D' }} />
                Inventory Management
              </h3>
              <div>{canManageInventory && <InventoryManagement />}</div>
            </div>
          </TabsContent>

          {/* Menu Management Tab */}
          <TabsContent
            value="menu"
            className="mt-0 animate-in fade-in-50 duration-300"
          >
            <div className="space-y-4">
              <h3 className="text-md font-semibold text-gray-800 dark:text-gray-200 flex items-center gap-2">
                <MenuSquare className="w-4 h-4" style={{ color: '#BD9A3D' }} />
                Menu Management
              </h3>
              <div>{canManageMenu && <MenuManagement />}</div>
            </div>
          </TabsContent>

          {/* Menu Scheduler Tab */}
          {/* <TabsContent
            value="scheduler"
            className="mt-0 animate-in fade-in-50 duration-300"
          >
            <div className="space-y-4">
              <h3 className="text-md font-semibold text-gray-800 dark:text-gray-200 flex items-center gap-2">
                <Calendar className="w-4 h-4" style={{ color: '#BD9A3D' }} />
                Menu Scheduler
              </h3>
              <div>{canScheduleMenu && <MenuSchedulerContent />}</div>
            </div>
          </TabsContent> */}

          {/* Orders Management Tab */}
          <TabsContent
            value="orders"
            className="mt-0 animate-in fade-in-50 duration-300"
          >
            <div className="space-y-4">
              <h3 className="text-md font-semibold text-gray-800 dark:text-gray-200 flex items-center gap-2">
                <ShoppingCart
                  className="w-4 h-4"
                  style={{ color: '#BD9A3D' }}
                />
                Orders Management
              </h3>

              <div>{<OrdersManagement orderType={orderType} />}</div>
            </div>
          </TabsContent>

          {/* Sales Management Tab */}
          <TabsContent
            value="sales"
            className="mt-0 animate-in fade-in-50 duration-300"
          >
            <div className="space-y-4">
              <h3 className="text-md font-semibold text-gray-800 dark:text-gray-200 flex items-center gap-2">
                <span
                  style={{
                    color: activeTab === 'sales' ? '#BD9A3D' : 'inherit',
                  }}
                >
                  &#8358;
                </span>
                Sales Management
              </h3>

              <div>{canAccessPOS && <SalesManagement />}</div>
            </div>
          </TabsContent>
        </Tabs>
      )}
    </div>
  );
}

// function MenuSchedulerContent() {
//   const [selectedWeek, setSelectedWeek] = useState(
//     new Date().toISOString().split('T')[0]
//   );

//   const { weeklyMenus, weeklyMenusLoading, mutate } =
//     GetWeeklyMenus(selectedWeek);
//   const { menu } = GetAllMenu('?limit=1000');

//   return (
//     <div className="space-y-6">
//       <div className="flex justify-between items-center">
//         <p className="text-muted-foreground">
//           Schedule weekly menus for patients
//         </p>
//         <div className="flex items-center gap-2">
//           <Calendar className="h-4 w-4" />
//           <input
//             type="week"
//             value={selectedWeek}
//             onChange={(e) => setSelectedWeek(e.target.value)}
//             className="border rounded px-3 py-2"
//           />
//         </div>
//       </div>

//       {/* {weeklyMenusLoading ? (
//         <div className="flex items-center justify-center h-64">
//           <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
//         </div>
//       ) : (
//         <WeeklyScheduler
//           weeklyMenus={weeklyMenus?.data}
//           availableMenus={menu?.data?.menu || []}
//           selectedWeek={selectedWeek}
//           mutate={mutate}
//         />
//       )} */}
//     </div>
//   );
// }
