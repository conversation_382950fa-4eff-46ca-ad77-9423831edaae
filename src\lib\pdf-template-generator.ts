import jsPDF from 'jspdf';

export const generatePractisingPrivilegesTemplate = (): Uint8Array => {
  const doc = new jsPDF();
  
  // Header
  doc.setFontSize(20);
  doc.setFont('helvetica', 'bold');
  doc.text('PRACTISING PRIVILEGES APPLICATION', 105, 30, { align: 'center' });
  
  // Subtitle
  doc.setFontSize(12);
  doc.setFont('helvetica', 'normal');
  doc.text('Please complete all sections of this form', 105, 45, { align: 'center' });
  
  // Draw a line
  doc.line(20, 55, 190, 55);
  
  // Form fields
  doc.setFontSize(11);
  doc.setFont('helvetica', 'bold');
  
  let yPos = 70;
  const leftMargin = 25;
  const fieldHeight = 20;
  
  // Personal Information Section
  doc.text('PERSONAL INFORMATION', leftMargin, yPos);
  yPos += 15;
  
  // Full Name
  doc.setFont('helvetica', 'normal');
  doc.text('Full Name:', leftMargin, yPos);
  doc.line(leftMargin + 25, yPos + 2, 185, yPos + 2);
  yPos += fieldHeight;
  
  // Medical License Number
  doc.text('Medical License Number:', leftMargin, yPos);
  doc.line(leftMargin + 50, yPos + 2, 185, yPos + 2);
  yPos += fieldHeight;
  
  // Specialty
  doc.text('Specialty:', leftMargin, yPos);
  doc.line(leftMargin + 25, yPos + 2, 185, yPos + 2);
  yPos += fieldHeight;
  
  // Years of Experience
  doc.text('Years of Experience:', leftMargin, yPos);
  doc.line(leftMargin + 45, yPos + 2, 185, yPos + 2);
  yPos += fieldHeight;
  
  // Previous Hospital
  doc.text('Previous Hospital/Institution:', leftMargin, yPos);
  doc.line(leftMargin + 60, yPos + 2, 185, yPos + 2);
  yPos += fieldHeight + 10;
  
  // Contact Information Section
  doc.setFont('helvetica', 'bold');
  doc.text('CONTACT INFORMATION', leftMargin, yPos);
  yPos += 15;
  
  doc.setFont('helvetica', 'normal');
  
  // Contact Number
  doc.text('Contact Number:', leftMargin, yPos);
  doc.line(leftMargin + 35, yPos + 2, 185, yPos + 2);
  yPos += fieldHeight;
  
  // Email Address
  doc.text('Email Address:', leftMargin, yPos);
  doc.line(leftMargin + 30, yPos + 2, 185, yPos + 2);
  yPos += fieldHeight;
  
  // Emergency Contact
  doc.text('Emergency Contact Name:', leftMargin, yPos);
  doc.line(leftMargin + 50, yPos + 2, 185, yPos + 2);
  yPos += fieldHeight;
  
  // Emergency Contact Number
  doc.text('Emergency Contact Number:', leftMargin, yPos);
  doc.line(leftMargin + 55, yPos + 2, 185, yPos + 2);
  yPos += fieldHeight + 10;
  
  // Privileges Section
  doc.setFont('helvetica', 'bold');
  doc.text('REQUESTED PRIVILEGES', leftMargin, yPos);
  yPos += 15;
  
  doc.setFont('helvetica', 'normal');
  doc.text('Please specify the clinical privileges you are requesting:', leftMargin, yPos);
  yPos += 10;
  
  // Draw lines for privileges
  for (let i = 0; i < 3; i++) {
    doc.line(leftMargin, yPos + 2, 185, yPos + 2);
    yPos += 15;
  }
  
  yPos += 10;
  
  // Additional Comments Section
  doc.setFont('helvetica', 'bold');
  doc.text('ADDITIONAL COMMENTS', leftMargin, yPos);
  yPos += 15;
  
  doc.setFont('helvetica', 'normal');
  
  // Draw lines for comments
  for (let i = 0; i < 3; i++) {
    doc.line(leftMargin, yPos + 2, 185, yPos + 2);
    yPos += 15;
  }
  
  yPos += 20;
  
  // Signature Section
  doc.setFont('helvetica', 'bold');
  doc.text('APPLICANT DECLARATION', leftMargin, yPos);
  yPos += 15;
  
  doc.setFont('helvetica', 'normal');
  doc.setFontSize(10);
  doc.text('I hereby declare that the information provided in this application is true and complete.', leftMargin, yPos);
  yPos += 10;
  doc.text('I understand that any false information may result in the denial of privileges.', leftMargin, yPos);
  yPos += 20;
  
  // Signature line
  doc.text('Applicant Signature:', leftMargin, yPos);
  doc.line(leftMargin + 40, yPos + 2, 120, yPos + 2);
  doc.text('Date:', 130, yPos);
  doc.line(140, yPos + 2, 185, yPos + 2);
  
  // Footer
  yPos += 30;
  doc.setFontSize(8);
  doc.text('For Office Use Only - Application received on: _______________', leftMargin, yPos);
  
  return new Uint8Array(doc.output('arraybuffer'));
};
