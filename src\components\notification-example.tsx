'use client';

import { Button } from '@/components/ui/button';
import { useNotifications } from '@/hooks/useNotifications';
import { createNotification } from '@/api/notifications/data';
import { toast } from 'sonner';

export function NotificationExample() {
  const { addNotification, refresh, notifications, unreadCount, isLoading } =
    useNotifications();

  const handleAddLocalNotification = () => {
    // Add a local notification (real-time, not persistent)
    addNotification({
      title: 'Local Notification',
      message: 'This is a local notification that will disappear on refresh',
      link: '/dashboard',
      type: 'local_test',
    });
  };

  const handleAddPersistentNotification = async () => {
    try {
      // Create a persistent notification via backend API
      await createNotification({
        title: 'Persistent Notification',
        message:
          'This notification is saved to the database and syncs across devices',
        link: '/dashboard',
        type: 'persistent_test',
        priority: 'medium',
        data: {
          source: 'demo',
          category: 'test',
          timestamp: new Date().toISOString(),
        },
      });

      // Refresh to show the new notification
      refresh();
      toast.success('Persistent notification created!');
    } catch (error) {
      toast.error('Failed to create persistent notification');
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium">Notification System Demo</h3>
        <p className="text-sm text-muted-foreground mt-2">
          This demonstrates both local (real-time) and persistent
          (database-backed) notifications.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="p-4 border rounded-lg">
          <h4 className="font-medium mb-2">Local Notifications</h4>
          <p className="text-sm text-muted-foreground mb-3">
            Real-time notifications that appear immediately but don't persist
            across sessions.
          </p>
          <Button
            onClick={handleAddLocalNotification}
            variant="outline"
            size="sm"
          >
            Add Local Notification
          </Button>
        </div>

        <div className="p-4 border rounded-lg">
          <h4 className="font-medium mb-2">Persistent Notifications</h4>
          <p className="text-sm text-muted-foreground mb-3">
            Database-backed notifications that sync across all devices and
            persist across sessions.
          </p>
          <Button
            onClick={handleAddPersistentNotification}
            variant="default"
            size="sm"
          >
            Add Persistent Notification
          </Button>
        </div>
      </div>

      <div className="p-4 bg-muted/50 rounded-lg">
        <h4 className="font-medium mb-2">Current Status</h4>
        <div className="grid grid-cols-3 gap-4 text-sm">
          <div>
            <span className="text-muted-foreground">Total:</span>
            <span className="ml-2 font-medium">{notifications.length}</span>
          </div>
          <div>
            <span className="text-muted-foreground">Unread:</span>
            <span className="ml-2 font-medium">{unreadCount}</span>
          </div>
          <div>
            <span className="text-muted-foreground">Loading:</span>
            <span className="ml-2 font-medium">{isLoading ? 'Yes' : 'No'}</span>
          </div>
        </div>
        <Button onClick={refresh} variant="ghost" size="sm" className="mt-2">
          Refresh from Backend
        </Button>
      </div>
    </div>
  );
}
