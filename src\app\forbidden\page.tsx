'use client';

import Image from 'next/image';
import { useRouter } from 'next/navigation';

export default function Forbidden() {
  const router = useRouter();

  return (
    <div className="flex min-h-[calc(100vh-64px)] items-center justify-center align">
      <div className="px-2 hidden sm:block">
        <Image
          src="/icon.png"
          alt="logo"
          width={70}
          height={70}
          quality={100}
        />
      </div>
      <div className="h-16 border"></div>
      <div className="px-4 text-sm text-primary">
        <h1 className="text-xl font-bold text-red-500">Error Notice</h1>
        <p>The page doesn't exist or you don't have the required access.</p>
        <p
          className="font-medium underline cursor-pointer"
          onClick={() => router.push('/')}
        >
          {' '}
          Go back
        </p>
      </div>
    </div>
  );
}
