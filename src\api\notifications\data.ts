import useSWR from 'swr';
import { myApi } from '@/api/fetcher';
import { toast } from 'sonner';

// Backend notification interface (matches your database schema)
export interface BackendNotification {
  id: string;
  title: string;
  message: string;
  isRead: boolean;
  readAt?: string; // ISO string when notification was read
  timestamp?: string; // ISO string from backend
  link?: string;
  type?: string;
  priority?: 'low' | 'medium' | 'high';
  data?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

export interface NotificationResponse {
  data: {
    notifications: BackendNotification[];
    pagination?: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  };
}

// Get user notifications from backend
export const GetUserNotifications = (params?: string) => {
  const qs = params ? new URLSearchParams(params) : new URLSearchParams();

  const { data, error, isLoading, mutate } = useSWR<NotificationResponse>(
    `/notifications/list?${qs.toString()}`
  );

  return {
    notifications: data?.data?.notifications || [],
    pagination: data?.data?.pagination,
    isLoading,
    error,
    mutate,
  };
};

// Get unread notification count
export const GetUnreadNotificationCount = () => {
  const { data, error, isLoading, mutate } = useSWR<{ count: number }>(
    '/notifications/unread-count'
  );

  return {
    unreadCount: data?.count || 0,
    isLoading,
    error,
    mutate,
  };
};

// Mark notification as read
export const markNotificationAsRead = async (notificationId: string) => {
  try {
    const response = await myApi.patch(
      `/notifications/${notificationId}/mark-read`
    );
    return response.data;
  } catch (error) {
    console.error('Error marking notification as read:', error);
    toast.error('Failed to mark notification as read');
    throw error;
  }
};

// Mark all notifications as read
export const markAllNotificationsAsRead = async () => {
  try {
    const response = await myApi.patch('/notifications/mark-all-read');
    toast.success('All notifications marked as read');
    return response.data;
  } catch (error) {
    console.error('Error marking all notifications as read:', error);
    toast.error('Failed to mark all notifications as read');
    throw error;
  }
};

// Delete notification
export const deleteNotification = async (notificationId: string) => {
  try {
    const response = await myApi.delete(
      `/notifications/delete/${notificationId}`
    );
    toast.success('Notification deleted');
    return response.data;
  } catch (error) {
    console.error('Error deleting notification:', error);
    toast.error('Failed to delete notification');
    throw error;
  }
};

// Clear all notifications
export const clearAllNotifications = async () => {
  try {
    const response = await myApi.delete('/notifications/clear-all');
    toast.success('All notifications cleared');
    return response.data;
  } catch (error) {
    console.error('Error clearing all notifications:', error);
    toast.error('Failed to clear all notifications');
    throw error;
  }
};

// Create notification (for testing or admin purposes)
export const createNotification = async (notificationData: {
  title: string;
  message: string;
  link?: string;
  type?: string;
  priority?: 'low' | 'medium' | 'high';
  data?: Record<string, any>; // This maps to the 'data' field in database
}) => {
  try {
    const response = await myApi.post('/notifications', notificationData);
    return response.data;
  } catch (error) {
    console.error('Error creating notification:', error);
    toast.error('Failed to create notification');
    throw error;
  }
};
