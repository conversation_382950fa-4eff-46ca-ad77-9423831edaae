import React, { useState } from 'react';
import Pagination from '@/components/common/pagination';
import dayjs from 'dayjs';
import { Loader2, Ellipsis, Trash2 } from 'lucide-react';
import { GetInvestigation } from '@/api/data';
import NewInvestigation from './new';
import { toast } from 'sonner';
import { myApi } from '@/api/fetcher';

interface InvestigationProps {
  openCreate: boolean;
  permitDelete: boolean;
  setOpenCreate: React.Dispatch<React.SetStateAction<boolean>>;
}

const Investigation: React.FC<InvestigationProps> = ({
  openCreate,
  permitDelete,
  setOpenCreate,
}) => {
  const [pageSize, setPageSize] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);

  const { investigation, investigationLoading, mutate, investigationError } =
    GetInvestigation(`?page=${currentPage}&limit=${pageSize}`);
  const data = investigation?.data?.investigations;
  const totalPages = investigation?.data?.totalPages ?? 0;

  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  const deleteInvestigation = async (id: number) => {
    try {
      const res = await myApi.delete(`/package/delete-investigation/${id}`);
      if (res.status === 200) {
        toast.success(res.data.data.message);
        if (mutate) {
          mutate();
        }
      }
    } catch (error) {
      console.log('Something went wrong');
    }
  };

  if (totalPages === 0) {
    <div className="border text-sm py-12 text-center dar:text-white">
      No data available
    </div>;
  }

  return (
    <>
      <div className="w-full overflow-x-auto rounded-t-lg shadow-md">
        <table className="w-full table-auto text-left text-xs">
          <thead className="bg-primary text-gray-100 dark:text-black">
            <tr>
              <th className="table-style">S/N</th>
              <th className="table-style">Date</th>
              <th className="table-style">Name</th>
              <th className="table-style">Delete</th>
            </tr>
          </thead>
          <tbody className="relative overflow-x-auto whitespace-nowrap text-sm shadow-md">
            {data?.map((dat: any, index: number) => (
              <tr
                className="text-xs text-[#062A55] dark:text-white"
                key={dat.id}
              >
                <td className="table-style">
                  {currentPage === 1
                    ? index + 1
                    : (currentPage - 1) * pageSize + (index + 1)}
                </td>
                <td className="table-style">
                  {dayjs(dat.createdAt).format('MMMM D, YYYY')}
                </td>
                <td className="table-style">{dat.name}</td>
                <td className="table-style">
                  {permitDelete ? (
                    <Trash2
                      onClick={() =>
                        toast.error('Delete Investigation', {
                          description:
                            "This action is irreversible. Click 'Delete' to confirm, or ignore to cancel.",
                          action: {
                            label: 'Delete',
                            onClick: () => deleteInvestigation(dat.id),
                          },
                        })
                      }
                      className="w-4 h-4 text-destructive cursor-pointer"
                    />
                  ) : (
                    <Ellipsis className="w-4 h-4 text-gray-400" />
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
        {totalPages === 0 && (
          <div className="border text-sm py-12 text-center">
            No data available
          </div>
        )}
        {investigationLoading && (
          <div className="bg-white py-24 text-center">
            <Loader2 className="animate-spin w-6 h-6 " />
            <span>Loading...</span>
          </div>
        )}
      </div>
      {totalPages > 1 ? (
        <Pagination
          title="Investigations"
          totalPages={totalPages}
          currentPage={currentPage}
          onPageChange={handlePageChange}
          totalCount={investigation?.data?.totalCount}
        />
      ) : (
        ''
      )}
      <NewInvestigation
        open={openCreate}
        setOpen={setOpenCreate}
        mutate={mutate}
      />
    </>
  );
};

export default Investigation;
